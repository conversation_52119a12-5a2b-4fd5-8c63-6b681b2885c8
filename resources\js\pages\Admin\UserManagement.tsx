import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { <PERSON>, CheckCircle, Eye, Filter, Plus, Search, Shield, ShieldCheck, Trash2, User<PERSON>heck, Users, UserX, Wallet, XCircle } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number | string;
    phone?: string;
    location?: string;
    created_at: string;
    projects_count: number;
    bids_count: number;
}

interface Props {
    users: {
        data: User[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        search?: string;
        role?: string;
        status?: string;
        verified?: string;
    };
    stats: {
        total_users: number;
        active_users: number;
        suspended_users: number;
        blocked_users: number;
        verified_users: number;
    };
    permissions: {
        can_delete_users: boolean;
        can_view_financial_data: boolean;
    };
}

export default function UserManagement({ users, filters, stats, permissions }: Props) {
    const { auth } = usePage<{ auth: { user: User } }>().props;
    const authUser = auth.user;

    const [selectedUser, setSelectedUser] = useState<User | null>(null);
    const [actionType, setActionType] = useState<'status' | 'verification' | 'wallet' | 'delete' | null>(null);

    const searchForm = useForm({
        search: filters.search || '',
        role: filters.role || 'all',
        status: filters.status || 'all',
        verified: filters.verified || 'all',
    });

    const actionForm = useForm({
        status: '',
        reason: '',
        is_verified: false,
        amount: '',
        type: 'subtract',
        wallet_reason: '',
    });

    const handleSearch = () => {
        router.get(route('admin.users.index'), searchForm.data, {
            preserveState: true,
            replace: true,
        });
    };

    const handleStatusUpdate = (user: User, status: string) => {
        setSelectedUser(user);
        setActionType('status');
        actionForm.setData('status', status);
    };

    const handleVerificationUpdate = (user: User, verified: boolean) => {
        setSelectedUser(user);
        setActionType('verification');
        actionForm.setData('is_verified', verified);
    };

    const handleWalletAdjustment = (user: User) => {
        setSelectedUser(user);
        setActionType('wallet');
        actionForm.reset();
    };

    const handleDelete = (user: User) => {
        setSelectedUser(user);
        setActionType('delete');
    };

    const executeAction = () => {
        if (!selectedUser || !actionType) return;

        const baseUrl = route('admin.users.show', selectedUser.id);

        switch (actionType) {
            case 'status':
                actionForm.put(route('admin.users.update-status', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
            case 'verification':
                actionForm.patch(route('admin.users.update-verification', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
            case 'wallet':
                actionForm.setData({
                    amount: actionForm.data.amount,
                    type: actionForm.data.type,
                    reason: actionForm.data.wallet_reason,
                });
                actionForm.post(route('admin.users.adjust-wallet', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
            case 'delete':
                actionForm.delete(route('admin.users.destroy', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
        }
    };

    const closeDialog = () => {
        setSelectedUser(null);
        setActionType(null);
        actionForm.reset();
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            active: 'bg-green-100 text-green-800',
            suspended: 'bg-yellow-100 text-yellow-800',
            blocked: 'bg-red-100 text-red-800',
        };
        return variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800';
    };

    const getRoleBadge = (role: string) => {
        const variants = {
            user: 'bg-blue-100 text-blue-800',
            admin: 'bg-purple-100 text-purple-800',
            super_admin: 'bg-red-100 text-red-800',
        };
        return variants[role as keyof typeof variants] || 'bg-gray-100 text-gray-800';
    };

    return (
        <AppLayout>
            <Head title="User Management" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8 flex items-start justify-between">
                        <div>
                            <h1 className="flex items-center gap-2 text-3xl font-bold text-gray-900">
                                <Shield className="h-8 w-8 text-red-600" />
                                User Management
                            </h1>
                            <p className="mt-2 text-gray-600">Manage all users on the platform - view, edit, suspend, or verify user accounts</p>
                        </div>
                        <Link href={route('admin.users.create')}>
                            <Button className="flex items-center gap-2">
                                <Plus className="h-4 w-4" />
                                Create User
                            </Button>
                        </Link>
                    </div>

                    {/* Stats Cards */}
                    <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-5">
                        <Card>
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Total Users</p>
                                        <p className="text-2xl font-bold">{stats.total_users}</p>
                                    </div>
                                    <Users className="h-8 w-8 text-blue-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Active</p>
                                        <p className="text-2xl font-bold text-green-600">{stats.active_users}</p>
                                    </div>
                                    <CheckCircle className="h-8 w-8 text-green-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Suspended</p>
                                        <p className="text-2xl font-bold text-yellow-600">{stats.suspended_users}</p>
                                    </div>
                                    <Ban className="h-8 w-8 text-yellow-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Blocked</p>
                                        <p className="text-2xl font-bold text-red-600">{stats.blocked_users}</p>
                                    </div>
                                    <XCircle className="h-8 w-8 text-red-600" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600">Verified</p>
                                        <p className="text-2xl font-bold text-blue-600">{stats.verified_users}</p>
                                    </div>
                                    <ShieldCheck className="h-8 w-8 text-blue-600" />
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters & Search
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                                <div>
                                    <Label htmlFor="search">Search</Label>
                                    <div className="relative">
                                        <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                        <Input
                                            id="search"
                                            placeholder="Name, email, phone..."
                                            value={searchForm.data.search}
                                            onChange={(e) => searchForm.setData('search', e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <Label>Role</Label>
                                    <Select value={searchForm.data.role} onValueChange={(value) => searchForm.setData('role', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Roles</SelectItem>
                                            <SelectItem value="user">User</SelectItem>
                                            <SelectItem value="admin">Admin</SelectItem>
                                            <SelectItem value="super_admin">Super Admin</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Label>Status</Label>
                                    <Select value={searchForm.data.status} onValueChange={(value) => searchForm.setData('status', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Status</SelectItem>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="suspended">Suspended</SelectItem>
                                            <SelectItem value="blocked">Blocked</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Label>Verification</Label>
                                    <Select value={searchForm.data.verified} onValueChange={(value) => searchForm.setData('verified', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All</SelectItem>
                                            <SelectItem value="verified">Verified</SelectItem>
                                            <SelectItem value="unverified">Unverified</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="flex items-end">
                                    <Button onClick={handleSearch} className="w-full">
                                        Apply Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Users Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Users ({users.total})</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">User</th>
                                            <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                                                Role & Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">Wallet</th>
                                            <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                                                Activity
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white">
                                        {users.data.map((user) => (
                                            <tr key={user.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div>
                                                        <div className="flex items-center gap-2">
                                                            <div className="text-sm font-medium text-gray-900">{user.name}</div>
                                                            {user.is_verified && <ShieldCheck className="h-4 w-4 text-green-500" />}
                                                        </div>
                                                        <div className="text-sm text-gray-500">{user.email}</div>
                                                        {user.phone && <div className="text-xs text-gray-400">{user.phone}</div>}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="space-y-1">
                                                        <Badge className={getRoleBadge(user.role)}>{user.role.replace('_', ' ').toUpperCase()}</Badge>
                                                        <Badge className={getStatusBadge(user.status)}>{user.status.toUpperCase()}</Badge>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        ₵{Number(user.wallet_balance || 0).toFixed(2)}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500">
                                                    <div>{user.projects_count} projects</div>
                                                    <div>{user.bids_count} bids</div>
                                                </td>
                                                <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" size="sm">
                                                                Actions
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuLabel>User Actions</DropdownMenuLabel>
                                                            <DropdownMenuSeparator />

                                                            <DropdownMenuItem asChild>
                                                                <Link href={route('admin.users.show', user.id)}>
                                                                    <Eye className="mr-2 h-4 w-4" />
                                                                    View Details
                                                                </Link>
                                                            </DropdownMenuItem>

                                                            {user.status !== 'active' && (
                                                                <DropdownMenuItem onClick={() => handleStatusUpdate(user, 'active')}>
                                                                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                                                    Activate
                                                                </DropdownMenuItem>
                                                            )}

                                                            {user.status !== 'suspended' && (
                                                                <DropdownMenuItem onClick={() => handleStatusUpdate(user, 'suspended')}>
                                                                    <Ban className="mr-2 h-4 w-4 text-yellow-600" />
                                                                    Suspend
                                                                </DropdownMenuItem>
                                                            )}

                                                            {user.status !== 'blocked' && (
                                                                <DropdownMenuItem onClick={() => handleStatusUpdate(user, 'blocked')}>
                                                                    <XCircle className="mr-2 h-4 w-4 text-red-600" />
                                                                    Block
                                                                </DropdownMenuItem>
                                                            )}

                                                            <DropdownMenuSeparator />

                                                            <DropdownMenuItem onClick={() => handleVerificationUpdate(user, !user.is_verified)}>
                                                                {user.is_verified ? (
                                                                    <>
                                                                        <UserX className="mr-2 h-4 w-4 text-orange-600" />
                                                                        Remove Verification
                                                                    </>
                                                                ) : (
                                                                    <>
                                                                        <UserCheck className="mr-2 h-4 w-4 text-green-600" />
                                                                        Verify User
                                                                    </>
                                                                )}
                                                            </DropdownMenuItem>

                                                            <DropdownMenuItem onClick={() => handleWalletAdjustment(user)}>
                                                                <Wallet className="mr-2 h-4 w-4 text-blue-600" />
                                                                Adjust Wallet
                                                            </DropdownMenuItem>

                                                            {user.role !== 'super_admin' &&
                                                                permissions.can_delete_users &&
                                                                authUser.role === 'super_admin' && (
                                                                    <>
                                                                        <DropdownMenuSeparator />
                                                                        <DropdownMenuItem onClick={() => handleDelete(user)} className="text-red-600">
                                                                            <Trash2 className="mr-2 h-4 w-4" />
                                                                            Delete User
                                                                        </DropdownMenuItem>
                                                                    </>
                                                                )}
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* Pagination */}
                            {users.last_page > 1 && (
                                <div className="mt-6 flex justify-center">
                                    <div className="flex space-x-2">
                                        {Array.from({ length: users.last_page }, (_, i) => i + 1).map((page) => (
                                            <Button
                                                key={page}
                                                variant={page === users.current_page ? 'default' : 'outline'}
                                                size="sm"
                                                onClick={() =>
                                                    router.get(route('admin.users.index'), {
                                                        ...searchForm.data,
                                                        page,
                                                    })
                                                }
                                            >
                                                {page}
                                            </Button>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Action Dialogs */}
            <Dialog open={!!selectedUser && !!actionType} onOpenChange={closeDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {actionType === 'status' && 'Update User Status'}
                            {actionType === 'verification' && 'Update Verification Status'}
                            {actionType === 'wallet' && 'Adjust Wallet Balance'}
                            {actionType === 'delete' && 'Delete User'}
                        </DialogTitle>
                        <DialogDescription>
                            {selectedUser && (
                                <>
                                    {actionType === 'status' && `Change status for ${selectedUser.name}`}
                                    {actionType === 'verification' &&
                                        `${actionForm.data.is_verified ? 'Verify' : 'Remove verification for'} ${selectedUser.name}`}
                                    {actionType === 'wallet' && `Adjust wallet balance for ${selectedUser.name}`}
                                    {actionType === 'delete' &&
                                        `This will permanently delete ${selectedUser.name}'s account and all associated data.`}
                                </>
                            )}
                        </DialogDescription>
                    </DialogHeader>

                    {actionType === 'status' && (
                        <div className="space-y-4">
                            <div>
                                <Label>Reason (optional)</Label>
                                <Input
                                    placeholder="Reason for status change..."
                                    value={actionForm.data.reason}
                                    onChange={(e) => actionForm.setData('reason', e.target.value)}
                                />
                            </div>
                        </div>
                    )}

                    {actionType === 'wallet' && (
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Action</Label>
                                    <Select value={actionForm.data.type} onValueChange={(value) => actionForm.setData('type', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="subtract">Subtract Funds</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label>Amount (₵)</Label>
                                    <Input
                                        type="number"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={actionForm.data.amount}
                                        onChange={(e) => actionForm.setData('amount', e.target.value)}
                                    />
                                </div>
                            </div>
                            <div>
                                <Label>Reason</Label>
                                <Input
                                    placeholder="Reason for wallet adjustment..."
                                    value={actionForm.data.wallet_reason}
                                    onChange={(e) => actionForm.setData('wallet_reason', e.target.value)}
                                    required
                                />
                            </div>
                            {selectedUser && (
                                <Alert>
                                    <AlertDescription>Current balance: ₵{Number(selectedUser.wallet_balance || 0).toFixed(2)}</AlertDescription>
                                </Alert>
                            )}
                        </div>
                    )}

                    <DialogFooter>
                        <Button variant="outline" onClick={closeDialog}>
                            Cancel
                        </Button>
                        <Button
                            onClick={executeAction}
                            disabled={actionForm.processing}
                            variant={actionType === 'delete' ? 'destructive' : 'default'}
                        >
                            {actionForm.processing ? 'Processing...' : 'Confirm'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
