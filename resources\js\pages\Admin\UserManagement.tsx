import ActionDialogs from '@/components/Admin/UserManagement/ActionDialogs';
import UserFilters from '@/components/Admin/UserManagement/UserFilters';
import UserStats from '@/components/Admin/UserManagement/UserStats';
import UserTable from '@/components/Admin/UserManagement/UserTable';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { Plus, Shield } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number | string;
    phone?: string;
    location?: string;
    created_at: string;
    projects_count: number;
    bids_count: number;
}

interface Props {
    users: {
        data: User[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        search?: string;
        role?: string;
        status?: string;
        verified?: string;
    };
    stats: {
        total_users: number;
        active_users: number;
        suspended_users: number;
        blocked_users: number;
        verified_users: number;
    };
    permissions: {
        can_delete_users: boolean;
        can_view_financial_data: boolean;
    };
}

export default function UserManagement({ users, filters, stats, permissions }: Props) {
    const { auth } = usePage<{ auth: { user: User } }>().props;
    const authUser = auth.user;

    const [selectedUser, setSelectedUser] = useState<User | null>(null);
    const [actionType, setActionType] = useState<'status' | 'verification' | 'wallet' | 'delete' | null>(null);

    const searchForm = useForm({
        search: filters.search || '',
        role: filters.role || 'all',
        status: filters.status || 'all',
        verified: filters.verified || 'all',
    });

    const actionForm = useForm({
        status: '',
        reason: '',
        is_verified: false,
        amount: '',
        type: 'subtract',
        wallet_reason: '',
    });

    const handleSearch = () => {
        router.get(route('admin.users.index'), searchForm.data, {
            preserveState: true,
            replace: true,
        });
    };

    const handleStatusUpdate = (user: User, status: string) => {
        setSelectedUser(user);
        setActionType('status');
        actionForm.setData('status', status);
    };

    const handleVerificationUpdate = (user: User, verified: boolean) => {
        setSelectedUser(user);
        setActionType('verification');
        actionForm.setData('is_verified', verified);
    };

    const handleWalletAdjustment = (user: User) => {
        setSelectedUser(user);
        setActionType('wallet');
        actionForm.reset();
    };

    const handleDelete = (user: User) => {
        setSelectedUser(user);
        setActionType('delete');
    };

    const executeAction = () => {
        if (!selectedUser || !actionType) return;

        switch (actionType) {
            case 'status':
                actionForm.put(route('admin.users.update-status', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
            case 'verification':
                actionForm.patch(route('admin.users.update-verification', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
            case 'wallet':
                actionForm.setData({
                    amount: actionForm.data.amount,
                    type: actionForm.data.type,
                    reason: actionForm.data.wallet_reason,
                });
                actionForm.post(route('admin.users.adjust-wallet', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
            case 'delete':
                actionForm.delete(route('admin.users.destroy', selectedUser.id), {
                    onSuccess: () => closeDialog(),
                });
                break;
        }
    };

    const closeDialog = () => {
        setSelectedUser(null);
        setActionType(null);
        actionForm.reset();
    };

    return (
        <AppLayout>
            <Head title="User Management" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8 flex items-start justify-between">
                        <div>
                            <h1 className="flex items-center gap-2 text-3xl font-bold text-gray-900">
                                <Shield className="h-8 w-8 text-red-600" />
                                User Management
                            </h1>
                            <p className="mt-2 text-gray-600">Manage all users on the platform - view, edit, suspend, or verify user accounts</p>
                        </div>
                        <Link href={route('admin.users.create')}>
                            <Button className="flex items-center gap-2">
                                <Plus className="h-4 w-4" />
                                Create User
                            </Button>
                        </Link>
                    </div>

                    {/* Stats Cards */}
                    <UserStats stats={stats} />

                    {/* Filters */}
                    <UserFilters searchForm={searchForm} onSearch={handleSearch} />

                    {/* Users Table */}
                    <UserTable
                        users={users}
                        searchForm={searchForm}
                        permissions={permissions}
                        authUser={authUser}
                        onStatusUpdate={handleStatusUpdate}
                        onVerificationUpdate={handleVerificationUpdate}
                        onWalletAdjustment={handleWalletAdjustment}
                        onDelete={handleDelete}
                    />
                </div>
            </div>

            {/* Action Dialogs */}
            <ActionDialogs
                selectedUser={selectedUser}
                actionType={actionType}
                actionForm={actionForm}
                onClose={closeDialog}
                onExecute={executeAction}
            />
        </AppLayout>
    );
}
