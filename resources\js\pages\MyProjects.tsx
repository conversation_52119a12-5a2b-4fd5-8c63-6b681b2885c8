import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { AlertCircle, Calendar, CheckCircle, Clock, DollarSign, Eye, FileText, Plus, Target, TrendingUp, User, Users, XCircle } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'My Projects',
        href: '/my-projects',
    },
];

interface Project {
    id: number;
    title: string;
    slug: string;
    description: string;
    budget_min?: number;
    budget_max?: number;
    budget_type: 'fixed' | 'negotiable';
    deadline?: string;
    category?: string;
    academic_level?: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    file_count: number;
    created_at: string;
    bids_count: number;
    assigned_freelancer?: {
        id: number;
        name: string;
    };
    accepted_bid_amount?: number;
    milestones_progress: number;
}

interface AssignedProject {
    id: number;
    title: string;
    slug: string;
    description: string;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    accepted_bid_amount?: number;
    deadline?: string;
    created_at: string;
    client: {
        id: number;
        name: string;
    };
    milestones_progress: number;
}

interface Bid {
    id: number;
    amount: number;
    proposal: string;
    delivery_days: number;
    status: 'pending' | 'accepted' | 'rejected';
    created_at: string;
    project: {
        id: number;
        title: string;
        slug: string;
        status: string;
        client: {
            id: number;
            name: string;
        };
    };
}

interface Stats {
    total_projects_posted: number;
    active_projects_posted: number;
    completed_projects_posted: number;
    total_projects_working: number;
    active_projects_working: number;
    completed_projects_working: number;
    total_bids_submitted: number;
    pending_bids: number;
    accepted_bids: number;
    total_spent: number;
    total_earned: number;
}

interface Props {
    postedProjects: Project[];
    assignedProjects: AssignedProject[];
    myBids: Bid[];
    stats: Stats;
}

export default function MyProjects({ postedProjects, assignedProjects, myBids, stats }: Props) {
    const formatCurrency = (amount: number) => `₵${Number(amount || 0).toFixed(2)}`;

    const getStatusBadge = (status: string) => {
        const variants = {
            open: { variant: 'default' as const, icon: AlertCircle, color: 'text-blue-600' },
            in_progress: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
            completed: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
            cancelled: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
            pending: { variant: 'outline' as const, icon: Clock, color: 'text-yellow-600' },
            accepted: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
            rejected: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
        };

        const config = variants[status as keyof typeof variants] || variants.open;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {status.replace('_', ' ').toUpperCase()}
            </Badge>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="My Projects" />

            <div className="space-y-8">
                {/* Header */}
                <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-2xl font-bold sm:text-3xl">My Projects</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Manage your projects, track work progress, and view your bidding activity
                        </p>
                    </div>
                    <div className="flex-shrink-0">
                        <Button asChild className="w-full sm:w-auto">
                            <Link href="/projects/create">
                                <Plus className="mr-2 h-4 w-4" />
                                <span className="hidden sm:inline">Post New Project</span>
                                <span className="sm:hidden">Post Project</span>
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Statistics Overview */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Projects Posted</p>
                                    <p className="text-2xl font-bold">{stats.total_projects_posted}</p>
                                    <p className="text-xs text-green-600">{stats.active_projects_posted} active</p>
                                </div>
                                <FileText className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Working On</p>
                                    <p className="text-2xl font-bold">{stats.total_projects_working}</p>
                                    <p className="text-xs text-blue-600">{stats.active_projects_working} in progress</p>
                                </div>
                                <Target className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Bids Submitted</p>
                                    <p className="text-2xl font-bold">{stats.total_bids_submitted}</p>
                                    <p className="text-xs text-yellow-600">{stats.pending_bids} pending</p>
                                </div>
                                <Users className="h-8 w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Total Earned</p>
                                    <p className="text-2xl font-bold">{formatCurrency(stats.total_earned)}</p>
                                    <p className="text-xs text-muted-foreground">Spent: {formatCurrency(stats.total_spent)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-orange-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Content Tabs */}
                <Tabs defaultValue="posted" className="space-y-6">
                    <TabsList className="grid w-full grid-cols-3 gap-1 sm:inline-flex sm:w-auto sm:grid-cols-none">
                        <TabsTrigger value="posted" className="text-xs sm:text-sm">
                            <span className="hidden sm:inline">Projects I Posted ({postedProjects.length})</span>
                            <span className="sm:hidden">Posted ({postedProjects.length})</span>
                        </TabsTrigger>
                        <TabsTrigger value="working" className="text-xs sm:text-sm">
                            <span className="hidden sm:inline">Projects I'm Working On ({assignedProjects.length})</span>
                            <span className="sm:hidden">Working ({assignedProjects.length})</span>
                        </TabsTrigger>
                        <TabsTrigger value="bids" className="text-xs sm:text-sm">
                            <span className="hidden sm:inline">My Bids ({myBids.length})</span>
                            <span className="sm:hidden">Bids ({myBids.length})</span>
                        </TabsTrigger>
                    </TabsList>

                    {/* Posted Projects Tab */}
                    <TabsContent value="posted" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Projects You've Posted</CardTitle>
                                <CardDescription>Manage projects you've created and track their progress</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {postedProjects.length > 0 ? (
                                    <div className="space-y-4">
                                        {postedProjects.map((project) => (
                                            <div key={project.id} className="rounded-lg border p-4">
                                                <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
                                                    <div className="min-w-0 flex-1">
                                                        <div className="mb-2 flex flex-col gap-2 sm:flex-row sm:items-center">
                                                            <Link
                                                                href={`/projects/${project.slug}`}
                                                                className="truncate text-lg font-semibold hover:text-blue-600"
                                                            >
                                                                {project.title}
                                                            </Link>
                                                            {getStatusBadge(project.status)}
                                                        </div>

                                                        <p className="mb-3 line-clamp-2 text-sm text-muted-foreground">{project.description}</p>

                                                        <div className="flex flex-wrap gap-3 text-sm text-muted-foreground sm:gap-4">
                                                            <div className="flex items-center gap-1">
                                                                <Users className="h-4 w-4" />
                                                                {project.bids_count} bids
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <Calendar className="h-4 w-4" />
                                                                {new Date(project.created_at).toLocaleDateString()}
                                                            </div>
                                                            {project.deadline && (
                                                                <div className="flex items-center gap-1">
                                                                    <Clock className="h-4 w-4" />
                                                                    Due: {new Date(project.deadline).toLocaleDateString()}
                                                                </div>
                                                            )}
                                                        </div>

                                                        {project.assigned_freelancer && (
                                                            <div className="mt-3 rounded-lg bg-green-50 p-3">
                                                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                                                                    <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-2">
                                                                        <div className="flex items-center gap-2">
                                                                            <User className="h-4 w-4 text-green-600" />
                                                                            <span className="text-sm font-medium">
                                                                                Assigned to: {project.assigned_freelancer.name}
                                                                            </span>
                                                                        </div>
                                                                        <span className="text-sm text-muted-foreground">
                                                                            for {formatCurrency(project.accepted_bid_amount || 0)}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                                {project.milestones_progress > 0 && (
                                                                    <div className="mt-2">
                                                                        <div className="mb-1 flex items-center justify-between text-xs text-muted-foreground">
                                                                            <span>Progress</span>
                                                                            <span>{Math.round(project.milestones_progress)}%</span>
                                                                        </div>
                                                                        <Progress value={project.milestones_progress} className="h-2" />
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>

                                                    <div className="mt-4 flex flex-row gap-2 sm:mt-0 sm:ml-4 sm:flex-col">
                                                        <Button size="sm" variant="outline" asChild className="flex-1 sm:flex-none">
                                                            <Link href={`/projects/${project.slug}`}>
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View
                                                            </Link>
                                                        </Button>
                                                        {project.status === 'in_progress' && (
                                                            <Button size="sm" variant="outline" asChild className="flex-1 sm:flex-none">
                                                                <Link href={`/projects/${project.slug}/milestones`}>
                                                                    <TrendingUp className="mr-2 h-4 w-4" />
                                                                    <span className="hidden sm:inline">Milestones</span>
                                                                    <span className="sm:hidden">Progress</span>
                                                                </Link>
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-12 text-center">
                                        <FileText className="mx-auto h-12 w-12 text-muted-foreground/50" />
                                        <h3 className="mt-4 text-lg font-semibold">No projects posted yet</h3>
                                        <p className="mt-2 text-muted-foreground">Start by posting your first project to get help with your work.</p>
                                        <Button className="mt-4" asChild>
                                            <Link href="/projects/create">
                                                <Plus className="mr-2 h-4 w-4" />
                                                Post Your First Project
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Working Projects Tab */}
                    <TabsContent value="working" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Projects You're Working On</CardTitle>
                                <CardDescription>Track progress on projects where you're the assigned freelancer</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {assignedProjects.length > 0 ? (
                                    <div className="space-y-4">
                                        {assignedProjects.map((project) => (
                                            <div key={project.id} className="rounded-lg border p-4">
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="mb-2 flex items-center gap-2">
                                                            <Link
                                                                href={`/projects/${project.slug}`}
                                                                className="text-lg font-semibold hover:text-blue-600"
                                                            >
                                                                {project.title}
                                                            </Link>
                                                            {getStatusBadge(project.status)}
                                                        </div>

                                                        <div className="mb-3 flex items-center gap-4 text-sm text-muted-foreground">
                                                            <div className="flex items-center gap-1">
                                                                <User className="h-4 w-4" />
                                                                Client: {project.client.name}
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <DollarSign className="h-4 w-4" />
                                                                {formatCurrency(project.accepted_bid_amount || 0)}
                                                            </div>
                                                            {project.deadline && (
                                                                <div className="flex items-center gap-1">
                                                                    <Clock className="h-4 w-4" />
                                                                    Due: {new Date(project.deadline).toLocaleDateString()}
                                                                </div>
                                                            )}
                                                        </div>

                                                        {project.milestones_progress > 0 && (
                                                            <div className="mt-3">
                                                                <div className="mb-1 flex items-center justify-between text-xs text-muted-foreground">
                                                                    <span>Progress</span>
                                                                    <span>{Math.round(project.milestones_progress)}%</span>
                                                                </div>
                                                                <Progress value={project.milestones_progress} className="h-2" />
                                                            </div>
                                                        )}
                                                    </div>

                                                    <div className="ml-4 flex flex-col gap-2">
                                                        <Button size="sm" variant="outline" asChild>
                                                            <Link href={`/projects/${project.slug}`}>
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View
                                                            </Link>
                                                        </Button>
                                                        {project.status === 'in_progress' && (
                                                            <Button size="sm" asChild>
                                                                <Link href={`/projects/${project.slug}/milestones`}>
                                                                    <TrendingUp className="mr-2 h-4 w-4" />
                                                                    Work on Milestones
                                                                </Link>
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-12 text-center">
                                        <Target className="mx-auto h-12 w-12 text-muted-foreground/50" />
                                        <h3 className="mt-4 text-lg font-semibold">No active work assignments</h3>
                                        <p className="mt-2 text-muted-foreground">Browse available projects and submit proposals to start working.</p>
                                        <Button className="mt-4" asChild>
                                            <Link href="/browse">Browse Projects</Link>
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* My Bids Tab */}
                    <TabsContent value="bids" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Your Bid History</CardTitle>
                                <CardDescription>Track all the proposals you've submitted to projects</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {myBids.length > 0 ? (
                                    <div className="space-y-4">
                                        {myBids.map((bid) => (
                                            <div key={bid.id} className="rounded-lg border p-4">
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="mb-2 flex items-center gap-2">
                                                            <Link
                                                                href={`/projects/${bid.project.slug}`}
                                                                className="text-lg font-semibold hover:text-blue-600"
                                                            >
                                                                {bid.project.title}
                                                            </Link>
                                                            {getStatusBadge(bid.status)}
                                                        </div>

                                                        <div className="mb-3 flex items-center gap-4 text-sm text-muted-foreground">
                                                            <div className="flex items-center gap-1">
                                                                <DollarSign className="h-4 w-4" />
                                                                Bid: {formatCurrency(bid.amount)}
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <Clock className="h-4 w-4" />
                                                                {bid.delivery_days} days delivery
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <User className="h-4 w-4" />
                                                                Client: {bid.project.client.name}
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <Calendar className="h-4 w-4" />
                                                                {new Date(bid.created_at).toLocaleDateString()}
                                                            </div>
                                                        </div>

                                                        <p className="line-clamp-2 text-sm text-muted-foreground">{bid.proposal}</p>
                                                    </div>

                                                    <Button size="sm" variant="outline" asChild>
                                                        <Link href={`/projects/${bid.project.slug}`}>
                                                            <Eye className="mr-2 h-4 w-4" />
                                                            View Project
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-12 text-center">
                                        <Users className="mx-auto h-12 w-12 text-muted-foreground/50" />
                                        <h3 className="mt-4 text-lg font-semibold">No bids submitted yet</h3>
                                        <p className="mt-2 text-muted-foreground">Start bidding on projects to build your freelancing portfolio.</p>
                                        <Button className="mt-4" asChild>
                                            <Link href="/browse">Browse Projects</Link>
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
