import { Card, CardContent } from '@/components/ui/card';
import { Activity, BarChart3, DollarSign, Target } from 'lucide-react';

interface FinancialStats {
    total_wallet_balance: number;
    total_transactions: number;
    total_deposits: number;
    total_withdrawals: number;
    pending_withdrawals: number;
    total_commission_earned?: number;
    pending_commission?: number;
    commission_withdrawn?: number;
    available_commission?: number;
    revenue_by_month: Array<{
        month: string;
        deposits: number;
        withdrawals: number;
    }>;
    commission_by_month?: Array<{
        month: string;
        total_commission: number;
    }>;
}

interface CommissionOverviewProps {
    financialStats: FinancialStats;
    permissions: {
        can_view_financial_data: boolean;
    };
    formatCurrency: (amount: number) => string;
}

export default function CommissionOverview({ financialStats, permissions, formatCurrency }: CommissionOverviewProps) {
    if (!permissions.can_view_financial_data || financialStats.total_commission_earned === undefined) {
        return null;
    }

    return (
        <>
            <div className="mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Platform Commission</h2>
                <p className="text-sm text-gray-600">Revenue generated from platform fees</p>
            </div>

            <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total Commission Earned</p>
                                <p className="text-3xl font-bold text-green-600">{formatCurrency(financialStats.total_commission_earned)}</p>
                                <p className="text-xs text-gray-500">Lifetime earnings</p>
                            </div>
                            <DollarSign className="h-12 w-12 text-green-600" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Available Commission</p>
                                <p className="text-3xl font-bold text-blue-600">{formatCurrency(financialStats.available_commission || 0)}</p>
                                <p className="text-xs text-gray-500">Ready for withdrawal</p>
                            </div>
                            <BarChart3 className="h-12 w-12 text-blue-600" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Pending Commission</p>
                                <p className="text-3xl font-bold text-yellow-600">{formatCurrency(financialStats.pending_commission || 0)}</p>
                                <p className="text-xs text-gray-500">Awaiting collection</p>
                            </div>
                            <Activity className="h-12 w-12 text-yellow-600" />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Withdrawn Commission</p>
                                <p className="text-3xl font-bold text-purple-600">{formatCurrency(financialStats.commission_withdrawn || 0)}</p>
                                <p className="text-xs text-gray-500">Total withdrawn</p>
                            </div>
                            <Target className="h-12 w-12 text-purple-600" />
                        </div>
                    </CardContent>
                </Card>
            </div>
        </>
    );
}
