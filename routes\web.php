<?php

use App\Http\Controllers\BidController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MilestoneController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\WalletController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Terms and Conditions page
Route::get('/terms', function () {
    return Inertia::render('terms');
})->name('terms');

// Paystack webhook route (outside auth middleware)
Route::post('webhooks/paystack', [WalletController::class, 'handlePaystackWebhook'])->name('webhooks.paystack');

Route::middleware(['auth', 'verified'])->group(function () {
    // Membership/Pricing page (requires authentication)
    Route::get('/membership', function () {
        return Inertia::render('membership');
    })->name('membership');
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Project routes
    Route::resource('projects', ProjectController::class);
    Route::get('browse', [ProjectController::class, 'index'])->name('browse');
    Route::get('my-projects', [DashboardController::class, 'myProjects'])->name('my-projects');
    Route::get('project-files/{file}/download', [ProjectController::class, 'downloadFile'])->name('project-files.download');

    // Bid routes
    Route::post('projects/{project}/bids', [BidController::class, 'store'])->name('bids.store');
    Route::patch('projects/{project}/bids/{bid}/accept', [BidController::class, 'accept'])->name('bids.accept');
    Route::patch('projects/{project}/bids/{bid}/reject', [BidController::class, 'reject'])->name('bids.reject');

    // Notification routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\NotificationController::class, 'showPage'])->name('index');
        Route::get('api', [App\Http\Controllers\NotificationController::class, 'index'])->name('api.index');
        Route::get('dropdown', [App\Http\Controllers\NotificationController::class, 'dropdown'])->name('dropdown');
        Route::patch('{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('read');
        Route::post('mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::delete('{id}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('destroy');
        Route::get('unread-count', [App\Http\Controllers\NotificationController::class, 'unreadCount'])->name('unread-count');
    });

    // Milestone routes
    Route::get('projects/{project}/milestones', [MilestoneController::class, 'index'])->name('milestones.index');
    Route::patch('projects/{project}/milestones/{milestone}/start', [MilestoneController::class, 'start'])->name('milestones.start');
    Route::patch('projects/{project}/milestones/{milestone}/submit', [MilestoneController::class, 'submit'])->name('milestones.submit');
    Route::patch('projects/{project}/milestones/{milestone}/approve', [MilestoneController::class, 'approve'])->name('milestones.approve');
    Route::patch('projects/{project}/milestones/{milestone}/revision', [MilestoneController::class, 'requestRevision'])->name('milestones.revision');

    // Wallet routes
    Route::prefix('wallet')->name('wallet.')->group(function () {
        Route::get('balance', [WalletController::class, 'balance'])->name('balance');
        Route::get('transactions', [WalletController::class, 'transactions'])->name('transactions');
        Route::get('add-funds', [WalletController::class, 'showAddFunds'])->name('add-funds');
        Route::post('deposit/initialize', [WalletController::class, 'initializeDeposit'])->name('deposit.initialize');
        Route::get('deposit/callback', [WalletController::class, 'handleDepositCallback'])->name('deposit.callback');
        Route::get('withdraw', [WalletController::class, 'showWithdraw'])->name('withdraw');
        Route::post('withdraw', [WalletController::class, 'processWithdrawal'])->name('withdraw.process');
    });

    // Chat routes
    Route::get('inbox', [ChatController::class, 'inbox'])->name('inbox');
    Route::get('api/conversations', [ChatController::class, 'index'])->name('conversations.index');
    Route::get('api/conversations/{conversation}', [ChatController::class, 'show'])->name('conversations.show');
    Route::post('api/conversations/{conversation}/messages', [ChatController::class, 'store'])->name('messages.store');
    Route::post('api/projects/{project}/conversations', [ChatController::class, 'createOrGet'])->name('conversations.create');

    // Rating routes
    Route::get('projects/{project}/rate', [App\Http\Controllers\RatingController::class, 'create'])->name('ratings.create');
    Route::post('projects/{project}/rate', [App\Http\Controllers\RatingController::class, 'store'])->name('ratings.store');
    Route::get('users/{user}/ratings', [App\Http\Controllers\RatingController::class, 'userRatings'])->name('users.ratings')->middleware('uuid.validate');

    // User Profile & Analytics routes
    Route::get('profile', [App\Http\Controllers\UserController::class, 'profile'])->name('profile.view');
    Route::get('analytics', [App\Http\Controllers\UserController::class, 'analytics'])->name('user.analytics');

    // Admin routes (Admin and Super Admin access)
    Route::prefix('admin')->name('admin.')->middleware(['admin'])->group(function () {
        Route::get('analytics', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('analytics.index');
        Route::get('users', [App\Http\Controllers\Admin\UserManagementController::class, 'index'])->name('users.index');
        Route::get('users/create', [App\Http\Controllers\Admin\UserManagementController::class, 'create'])->name('users.create');
        Route::post('users', [App\Http\Controllers\Admin\UserManagementController::class, 'store'])->name('users.store');
        Route::get('users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'show'])->name('users.show');
        Route::put('users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'update'])->name('users.update');
        Route::put('users/{user}/status', [App\Http\Controllers\Admin\UserManagementController::class, 'updateStatus'])->name('users.update-status');
        Route::patch('users/{user}/verification', [App\Http\Controllers\Admin\UserManagementController::class, 'updateVerification'])->name('users.update-verification');
        Route::post('users/{user}/wallet', [App\Http\Controllers\Admin\UserManagementController::class, 'adjustWallet'])->name('users.adjust-wallet');

        // Super Admin only routes
        Route::middleware('super_admin')->group(function () {
            Route::delete('users/{user}', [App\Http\Controllers\Admin\UserManagementController::class, 'destroy'])->name('users.destroy');
        });
    });
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';

// Debug route for wallet testing (remove in production)
Route::middleware(['auth'])->get('/debug/wallet-test', function () {
    try {
        $results = [];

        // Test 1: Database connection
        try {
            $userCount = \Illuminate\Support\Facades\DB::table('users')->count();
            $results['database'] = ['status' => 'OK', 'user_count' => $userCount];
        } catch (\Exception $e) {
            $results['database'] = ['status' => 'ERROR', 'message' => $e->getMessage()];
        }

        // Test 2: Cache system
        try {
            \Illuminate\Support\Facades\Cache::put('test_key', 'test_value', 60);
            $testValue = \Illuminate\Support\Facades\Cache::get('test_key');
            $results['cache'] = ['status' => $testValue === 'test_value' ? 'OK' : 'ERROR'];
        } catch (\Exception $e) {
            $results['cache'] = ['status' => 'ERROR', 'message' => $e->getMessage()];
        }

        // Test 3: Paystack API connectivity
        try {
            $secretKey = config('services.paystack.secret_key');
            $response = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $secretKey,
            ])->get('https://api.paystack.co/bank');

            $results['paystack_api'] = [
                'status' => $response->successful() ? 'OK' : 'ERROR',
                'http_status' => $response->status(),
                'has_secret_key' => !empty($secretKey)
            ];
        } catch (\Exception $e) {
            $results['paystack_api'] = ['status' => 'ERROR', 'message' => $e->getMessage()];
        }

        // Test 4: User authentication
        $user = \Illuminate\Support\Facades\Auth::user();
        $results['auth'] = [
            'status' => $user ? 'OK' : 'ERROR',
            'user_id' => $user?->id,
            'user_email' => $user?->email
        ];

        // Test 5: Test payment initialization logic
        if ($user) {
            try {
                $amount = 10 * 100; // ₵10 in kobo
                $reference = 'test_' . time() . '_' . $user->id;

                $response = \Illuminate\Support\Facades\Http::withHeaders([
                    'Authorization' => 'Bearer ' . config('services.paystack.secret_key'),
                    'Content-Type' => 'application/json',
                ])->post('https://api.paystack.co/transaction/initialize', [
                    'email' => $user->email,
                    'amount' => $amount,
                    'reference' => $reference,
                    'callback_url' => route('wallet.deposit.callback'),
                    'metadata' => [
                        'user_id' => $user->id,
                        'type' => 'deposit',
                    ],
                ]);

                $results['payment_init_test'] = [
                    'status' => $response->successful() ? 'OK' : 'ERROR',
                    'http_status' => $response->status(),
                    'response_body' => $response->json(),
                ];
            } catch (\Exception $e) {
                $results['payment_init_test'] = ['status' => 'ERROR', 'message' => $e->getMessage()];
            }
        }

        return response()->json($results, 200);
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Debug test failed',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});
