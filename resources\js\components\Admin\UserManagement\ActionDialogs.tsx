import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number | string;
    phone?: string;
    location?: string;
    created_at: string;
    projects_count: number;
    bids_count: number;
}

interface ActionDialogsProps {
    selectedUser: User | null;
    actionType: 'status' | 'verification' | 'wallet' | 'delete' | null;
    actionForm: {
        data: {
            status: string;
            reason: string;
            is_verified: boolean;
            amount: string;
            type: string;
            wallet_reason: string;
        };
        setData: (key: string, value: any) => void;
        processing: boolean;
    };
    onClose: () => void;
    onExecute: () => void;
}

export default function ActionDialogs({ selectedUser, actionType, actionForm, onClose, onExecute }: ActionDialogsProps) {
    const isOpen = !!selectedUser && !!actionType;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>
                        {actionType === 'status' && 'Update User Status'}
                        {actionType === 'verification' && 'Update Verification Status'}
                        {actionType === 'wallet' && 'Adjust Wallet Balance'}
                        {actionType === 'delete' && 'Delete User'}
                    </DialogTitle>
                    <DialogDescription>
                        {selectedUser && (
                            <>
                                {actionType === 'status' && `Change status for ${selectedUser.name}`}
                                {actionType === 'verification' &&
                                    `${actionForm.data.is_verified ? 'Verify' : 'Remove verification for'} ${selectedUser.name}`}
                                {actionType === 'wallet' && `Adjust wallet balance for ${selectedUser.name}`}
                                {actionType === 'delete' && `This will permanently delete ${selectedUser.name}'s account and all associated data.`}
                            </>
                        )}
                    </DialogDescription>
                </DialogHeader>

                {actionType === 'status' && (
                    <div className="space-y-4">
                        <div>
                            <Label>Reason (optional)</Label>
                            <Input
                                placeholder="Reason for status change..."
                                value={actionForm.data.reason}
                                onChange={(e) => actionForm.setData('reason', e.target.value)}
                            />
                        </div>
                    </div>
                )}

                {actionType === 'wallet' && (
                    <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label>Action</Label>
                                <Select value={actionForm.data.type} onValueChange={(value) => actionForm.setData('type', value)}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="subtract">Subtract Funds</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label>Amount (₵)</Label>
                                <Input
                                    type="number"
                                    step="0.01"
                                    placeholder="0.00"
                                    value={actionForm.data.amount}
                                    onChange={(e) => actionForm.setData('amount', e.target.value)}
                                />
                            </div>
                        </div>
                        <div>
                            <Label>Reason</Label>
                            <Input
                                placeholder="Reason for wallet adjustment..."
                                value={actionForm.data.wallet_reason}
                                onChange={(e) => actionForm.setData('wallet_reason', e.target.value)}
                                required
                            />
                        </div>
                        {selectedUser && (
                            <Alert>
                                <AlertDescription>Current balance: ₵{Number(selectedUser.wallet_balance || 0).toFixed(2)}</AlertDescription>
                            </Alert>
                        )}
                    </div>
                )}

                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>
                        Cancel
                    </Button>
                    <Button onClick={onExecute} disabled={actionForm.processing} variant={actionType === 'delete' ? 'destructive' : 'default'}>
                        {actionForm.processing ? 'Processing...' : 'Confirm'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
