<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Bid;
use App\Models\PlatformCommission;
use App\Models\Project;
use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class AnalyticsController extends Controller
{
    /**
     * Display admin analytics dashboard.
     */
    public function index(Request $request)
    {
        // Date range filter (default to last 30 days)
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        $user = Auth::user();
        $canViewFinancials = $user->isSuperAdmin();

        // User Statistics
        $userStats = [
            'total_users' => User::count(),
            'new_users_this_month' => User::whereMonth('created_at', now()->month)->count(),
            'active_users' => User::where('status', 'active')->count(),
            'verified_users' => User::where('is_verified', true)->count(),
            'users_by_role' => User::select('role', DB::raw('count(*) as count'))
                ->groupBy('role')
                ->pluck('count', 'role')
                ->toArray(),
        ];

        // Project Statistics
        $projectStats = [
            'total_projects' => Project::count(),
            'open_projects' => Project::where('status', 'open')->count(),
            'in_progress_projects' => Project::where('status', 'in_progress')->count(),
            'completed_projects' => Project::where('status', 'completed')->count(),
            'projects_by_category' => Project::select('category', DB::raw('count(*) as count'))
                ->whereNotNull('category')
                ->groupBy('category')
                ->orderByDesc('count')
                ->limit(10)
                ->pluck('count', 'category')
                ->toArray(),
            'projects_by_academic_level' => Project::select('academic_level', DB::raw('count(*) as count'))
                ->whereNotNull('academic_level')
                ->groupBy('academic_level')
                ->pluck('count', 'academic_level')
                ->toArray(),
        ];

        // Financial Statistics - Only for super admins
        $financialStats = [];
        if ($canViewFinancials) {
            $financialStats = [
                'total_wallet_balance' => User::sum('wallet_balance'),
                'total_transactions' => WalletTransaction::count(),
                'total_deposits' => WalletTransaction::where('type', 'deposit')->where('status', 'completed')->sum('amount'),
                'total_withdrawals' => WalletTransaction::where('type', 'withdrawal')->where('status', 'completed')->sum('amount'),
                'pending_withdrawals' => WalletTransaction::where('type', 'withdrawal')->where('status', 'pending')->sum('amount'),

                // Platform Commission Statistics
                'total_commission_earned' => PlatformCommission::where('status', 'collected')->sum('amount'),
                'pending_commission' => PlatformCommission::where('status', 'pending')->sum('amount'),
                'commission_withdrawn' => PlatformCommission::whereNotNull('withdrawn_at')->sum('amount'),
                'available_commission' => PlatformCommission::where('status', 'collected')->whereNull('withdrawn_at')->sum('amount'),

                'revenue_by_month' => WalletTransaction::select(
                    DB::raw('TO_CHAR(created_at, \'YYYY-MM\') as month'),
                    DB::raw('SUM(CASE WHEN type = \'deposit\' AND status = \'completed\' THEN amount ELSE 0 END) as deposits'),
                    DB::raw('SUM(CASE WHEN type = \'withdrawal\' AND status = \'completed\' THEN amount ELSE 0 END) as withdrawals')
                )
                    ->whereYear('created_at', now()->year)
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get()
                    ->toArray(),

                'commission_by_month' => PlatformCommission::select(
                    DB::raw('TO_CHAR(collected_at, \'YYYY-MM\') as month'),
                    DB::raw('SUM(amount) as total_commission')
                )
                    ->where('status', 'collected')
                    ->whereYear('collected_at', now()->year)
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get()
                    ->toArray(),
            ];
        }

        // Bid Statistics - Basic stats for all admins
        $bidStats = [
            'total_bids' => Bid::count(),
            'accepted_bids' => Bid::where('status', 'accepted')->count(),
            'pending_bids' => Bid::where('status', 'pending')->count(),
            'rejected_bids' => Bid::where('status', 'rejected')->count(),
        ];

        // Only show financial bid data to super admins
        if ($canViewFinancials) {
            $bidStats['average_bid_amount'] = Bid::avg('amount');
        }

        // Recent Activity - Exclude financial data for regular admins
        $recentActivity = [
            'recent_users' => User::latest()->limit(5)->get(['id', 'name', 'email', 'created_at', 'role']),
            'recent_projects' => Project::with('user:id,name')->latest()->limit(5)->get(['id', 'title', 'user_id', 'status', 'created_at']),
        ];

        // Only show transaction data to super admins
        if ($canViewFinancials) {
            $recentActivity['recent_transactions'] = WalletTransaction::with('user:id,name')
                ->latest()
                ->limit(10)
                ->get(['id', 'user_id', 'type', 'amount', 'status', 'created_at']);

            $recentActivity['recent_commissions'] = PlatformCommission::with(['project:id,title', 'milestone:id,title'])
                ->latest()
                ->limit(10)
                ->get(['id', 'project_id', 'milestone_id', 'amount', 'status', 'collected_at']);
        }

        // Growth Metrics (last 12 months)
        $growthMetrics = [
            'user_growth' => User::select(
                DB::raw('TO_CHAR(created_at, \'YYYY-MM\') as month'),
                DB::raw('COUNT(*) as count')
            )
                ->where('created_at', '>=', now()->subMonths(12))
                ->groupBy('month')
                ->orderBy('month')
                ->get()
                ->toArray(),
            'project_growth' => Project::select(
                DB::raw('TO_CHAR(created_at, \'YYYY-MM\') as month'),
                DB::raw('COUNT(*) as count')
            )
                ->where('created_at', '>=', now()->subMonths(12))
                ->groupBy('month')
                ->orderBy('month')
                ->get()
                ->toArray(),
        ];

        // Platform Health Metrics - Exclude financial metrics for regular admins
        $healthMetrics = [
            'project_completion_rate' => Project::where('status', 'completed')->count() / max(Project::count(), 1) * 100,
            'user_verification_rate' => User::where('is_verified', true)->count() / max(User::count(), 1) * 100,
            'bid_acceptance_rate' => Bid::where('status', 'accepted')->count() / max(Bid::count(), 1) * 100,
        ];

        // Only show average project value to super admins
        if ($canViewFinancials) {
            $healthMetrics['average_project_value'] = Project::whereNotNull('accepted_bid_amount')->avg('accepted_bid_amount');
        }

        return Inertia::render('Admin/Analytics', [
            'userStats' => $userStats,
            'projectStats' => $projectStats,
            'financialStats' => $financialStats,
            'bidStats' => $bidStats,
            'recentActivity' => $recentActivity,
            'growthMetrics' => $growthMetrics,
            'healthMetrics' => $healthMetrics,
            'permissions' => [
                'can_view_financial_data' => $canViewFinancials,
            ],
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ]);
    }
}
