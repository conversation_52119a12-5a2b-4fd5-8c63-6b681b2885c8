<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string|min:50',
            'requirements' => 'nullable|string',
            'budget_min' => 'nullable|numeric|min:0',
            'budget_max' => 'nullable|numeric|min:0|gte:budget_min',
            'budget_type' => 'required|in:fixed,negotiable',
            'deadline' => 'nullable|date|after:today',
            'category' => 'required|string|max:255',
            'academic_level' => 'required|string|max:255',
            'files' => 'nullable|array|max:5',
            'files.*' => 'file|mimes:pdf,docx,doc|max:10240', // 10MB max per file
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Project title is required.',
            'description.required' => 'Project description is required.',
            'description.min' => 'Project description must be at least 50 characters.',
            'budget_max.gte' => 'Maximum budget must be greater than or equal to minimum budget.',
            'deadline.after' => 'Deadline must be a future date.',
            'category.required' => 'Please select a project category.',
            'academic_level.required' => 'Please select an academic level.',
            'files.max' => 'You can upload a maximum of 5 files.',
            'files.*.mimes' => 'Only PDF and Word documents (.pdf, .docx, .doc) are allowed.',
            'files.*.max' => 'Each file must be smaller than 10MB.',
        ];
    }
}
