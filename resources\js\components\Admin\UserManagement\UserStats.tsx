import { <PERSON>, CardContent } from '@/components/ui/card';
import { <PERSON>, CheckCircle, ShieldCheck, Users, XCircle } from 'lucide-react';

interface UserStatsProps {
    stats: {
        total_users: number;
        active_users: number;
        suspended_users: number;
        blocked_users: number;
        verified_users: number;
    };
}

export default function UserStats({ stats }: UserStatsProps) {
    return (
        <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-5">
            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Total Users</p>
                            <p className="text-2xl font-bold">{stats.total_users}</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Active</p>
                            <p className="text-2xl font-bold text-green-600">{stats.active_users}</p>
                        </div>
                        <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Suspended</p>
                            <p className="text-2xl font-bold text-yellow-600">{stats.suspended_users}</p>
                        </div>
                        <Ban className="h-8 w-8 text-yellow-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Blocked</p>
                            <p className="text-2xl font-bold text-red-600">{stats.blocked_users}</p>
                        </div>
                        <XCircle className="h-8 w-8 text-red-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Verified</p>
                            <p className="text-2xl font-bold text-blue-600">{stats.verified_users}</p>
                        </div>
                        <ShieldCheck className="h-8 w-8 text-blue-600" />
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
