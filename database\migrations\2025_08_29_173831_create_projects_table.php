<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('slug')->nullable();
            $table->text('description');
            $table->text('requirements')->nullable();
            $table->decimal('budget_min', 10, 2)->nullable();
            $table->decimal('budget_max', 10, 2)->nullable();
            $table->string('budget_type')->default('fixed'); // fixed, hourly, negotiable
            $table->date('deadline')->nullable();
            $table->string('category')->nullable(); // Computer Science, Nursing, etc.
            $table->string('academic_level')->nullable(); // Bachelor, Master, PhD
            $table->enum('status', ['open', 'in_progress', 'completed', 'cancelled'])->default('open');
            $table->integer('file_count')->default(0);

            // Bid acceptance fields
            $table->foreignUuid('assigned_freelancer_id')->nullable()->constrained('users')->onDelete('set null');
            $table->decimal('accepted_bid_amount', 10, 2)->nullable();
            $table->timestamp('assigned_at')->nullable();

            // Escrow and milestone fields
            $table->decimal('escrow_amount', 10, 2)->nullable();
            $table->enum('escrow_status', ['none', 'held', 'partially_released', 'fully_released', 'refunded'])->default('none');
            $table->integer('total_milestones')->default(8);
            $table->integer('completed_milestones')->default(0);
            $table->decimal('total_released', 10, 2)->default(0);
            $table->decimal('total_commission', 10, 2)->default(0);
            $table->timestamp('escrow_created_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
