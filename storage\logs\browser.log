[2025-08-31 18:06:42] local.ERROR: Uncaught TypeError: n.toFixed is not a function http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js 6 1504 TypeError n.toFixed is not a function TypeError: n.toFixed is not a function
    at le (http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js:6:1504)
    at $o (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:34139)
    at os (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:62258)
    at sp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:72760)
    at Bp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:106782)
    at Ob (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105848)
    at Ns (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105680)
    at Cp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:102792)
    at Fp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:114218)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:99:1597) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:06:41.796Z"} 
[2025-08-31 18:06:42] local.ERROR: Uncaught TypeError: n.toFixed is not a function http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js 6 1504 TypeError n.toFixed is not a function TypeError: n.toFixed is not a function
    at le (http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js:6:1504)
    at $o (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:34139)
    at os (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:62258)
    at sp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:72760)
    at Bp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:106782)
    at Ob (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105848)
    at Ns (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105680)
    at Cp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:102792)
    at Fp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:114218)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:99:1597) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:06:41.796Z"} 
[2025-08-31 18:13:37] local.ERROR: Uncaught TypeError: balance.toFixed is not a function http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx 114 21 TypeError balance.toFixed is not a function TypeError: balance.toFixed is not a function
    at Withdraw (http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx:114:21)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10359:46) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:13:37.069Z"} 
[2025-08-31 18:13:37] local.ERROR: Uncaught TypeError: balance.toFixed is not a function http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx 114 21 TypeError balance.toFixed is not a function TypeError: balance.toFixed is not a function
    at Withdraw (http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx:114:21)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10359:46) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:13:37.070Z"} 
[2025-08-31 18:13:37] local.WARNING: %s

%s An error occurred in the <Withdraw> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:13:37.070Z"} 
[2025-08-31 18:28:44] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:28:43.608Z"} 
[2025-09-01 11:48:04] local.ERROR: Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}? true inert Although this works, it will not work as expected if you pass the string "false". inert true {"url":"http://127.0.0.1:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T11:47:58.894Z"} 
[2025-09-01 11:56:29] local.ERROR: Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}? true inert Although this works, it will not work as expected if you pass the string "false". inert true {"url":"http://127.0.0.1:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T11:56:27.396Z"} 
[2025-09-01 12:04:00] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T12:00:09.191Z"} 
[2025-09-01 12:27:12] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T12:27:12.202Z"} 
[2025-09-01 21:35:34] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T12:49:17.172Z"} 
[2025-09-01 22:08:28] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:08:27.197Z"} 
[2025-09-01 22:12:10] local.ERROR: In HTML, %s cannot be a descendant of <%s>.
This will cause a hydration error.%s <ul> p ...
    <FocusScope asChild={true} loop={true} trapped={true} onMountAutoFocus={undefined} ...>
      <Primitive.div tabIndex={-1} asChild={true} ref={function} onKeyDown={function}>
        <Primitive.div.Slot tabIndex={-1} onKeyDown={function} ref={function}>
          <Primitive.div.SlotClone tabIndex={-1} onKeyDown={function} ref={function}>
            <DismissableLayer role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
              <Primitive.div role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
                <div role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
                  <DialogHeader>
                    <div data-slot="dialog-header" className="flex flex-...">
                      <DialogTitle>
                      <DialogDescription>
                        <DialogDescription data-slot="dialog-des..." className="text-muted...">
                          <Primitive.p id="radix-«r1j»" data-slot="dialog-des..." className="text-muted..." ref={null}>
>                           <p
>                             id="radix-«r1j»"
>                             data-slot="dialog-description"
>                             className="text-muted-foreground text-sm"
>                             ref={null}
>                           >
                              <strong>
                              <br>
                              <br>
                              <span>
>                             <ul className="mt-2 list-inside list-disc space-y-1 text-sm">
                  ...
                  ...
    ... {"url":"http://127.0.0.1:8000/projects/legal-chat-bot-for-ghana","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:12:09.453Z"} 
[2025-09-01 22:12:10] local.ERROR: <%s> cannot contain a nested %s.
See this log for the ancestor stack trace. p <ul> {"url":"http://127.0.0.1:8000/projects/legal-chat-bot-for-ghana","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:12:09.453Z"} 
[2025-09-01 22:20:03] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:20:01.046Z"} 
[2025-09-01 22:20:08] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:20:06.715Z"} 
[2025-09-01 22:20:14] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:20:13.073Z"} 
[2025-09-02 07:24:55] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:24:52.519Z"} 
[2025-09-02 07:30:02] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-02T07:29:58.413Z"} 
[2025-09-02 07:40:07] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:40:04.926Z"} 
[2025-09-02 07:47:08] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:47:07.430Z"} 
[2025-09-02 07:47:48] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:47:48.629Z"} 
[2025-09-02 07:49:25] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:49:02.652Z"} 
[2025-09-02 07:50:51] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:50:51.014Z"} 
[2025-09-02 07:52:22] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:52:22.284Z"} 
[2025-09-02 07:56:06] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:56:06.332Z"} 
[2025-09-02 07:57:28] local.WARNING: Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}. {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:57:28.664Z"} 
[2025-09-02 07:59:24] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:59:24.754Z"} 
[2025-09-02 07:59:41] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-02T07:59:41.103Z"} 
[2025-09-02 08:01:05] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:01:05.560Z"} 
[2025-09-02 08:07:19] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:07:18.720Z"} 
[2025-09-02 08:07:24] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:07:24.555Z"} 
[2025-09-02 08:07:54] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:07:54.273Z"} 
[2025-09-02 08:09:30] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:09:30.282Z"} 
[2025-09-02 08:12:01] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:12:00.667Z"} 
[2025-09-02 08:12:51] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:12:51.182Z"} 
[2025-09-02 08:13:06] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:06.103Z"} 
[2025-09-02 08:13:21] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:21.715Z"} 
[2025-09-02 08:13:39] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:38.936Z"} 
[2025-09-02 08:13:52] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:52.615Z"} 
[2025-09-02 08:14:19] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:14:18.490Z"} 
[2025-09-02 08:14:38] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:14:38.026Z"} 
[2025-09-02 08:14:48] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:14:47.975Z"} 
[2025-09-02 08:15:30] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:15:30.694Z"} 
[2025-09-02 08:18:34] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:18:34.325Z"} 
[2025-09-02 08:18:54] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:18:53.477Z"} 
[2025-09-02 08:19:09] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:19:09.249Z"} 
[2025-09-02 08:22:16] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:22:16.466Z"} 
[2025-09-02 08:24:50] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:24:49.487Z"} 
[2025-09-02 08:25:46] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:25:46.040Z"} 
[2025-09-02 08:26:44] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:26:43.852Z"} 
[2025-09-02 08:36:09] local.ERROR: Error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:36:04.864Z"} 
[2025-09-02 18:16:29] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:16:28.972Z"} 
[2025-09-02 18:24:35] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/football-flutter-mobile-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:24:34.817Z"} 
[2025-09-02 18:24:36] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/football-flutter-mobile-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:24:34.821Z"} 
[2025-09-02 18:36:17] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:36:16.033Z"} 
[2025-09-02 19:33:41] local.ERROR: In HTML, %s cannot be a descendant of <%s>.
This will cause a hydration error.%s <ul> p ...
    <FocusScope asChild={true} loop={true} trapped={true} onMountAutoFocus={undefined} ...>
      <Primitive.div tabIndex={-1} asChild={true} ref={function} onKeyDown={function}>
        <Primitive.div.Slot tabIndex={-1} onKeyDown={function} ref={function}>
          <Primitive.div.SlotClone tabIndex={-1} onKeyDown={function} ref={function}>
            <DismissableLayer role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
              <Primitive.div role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
                <div role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
                  <DialogHeader>
                    <div data-slot="dialog-header" className="flex flex-...">
                      <DialogTitle>
                      <DialogDescription>
                        <DialogDescription data-slot="dialog-des..." className="text-muted...">
                          <Primitive.p id="radix-«r1j»" data-slot="dialog-des..." className="text-muted..." ref={null}>
>                           <p
>                             id="radix-«r1j»"
>                             data-slot="dialog-description"
>                             className="text-muted-foreground text-sm"
>                             ref={null}
>                           >
                              <strong>
                              <br>
                              <br>
                              <span>
>                             <ul className="mt-2 list-inside list-disc space-y-1 text-sm">
                  ...
                  ...
    ... {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T19:33:40.374Z"} 
[2025-09-02 19:33:41] local.ERROR: <%s> cannot contain a nested %s.
See this log for the ancestor stack trace. p <ul> {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T19:33:40.374Z"} 
[2025-09-02 21:02:11] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:02:10.989Z"} 
[2025-09-02 21:03:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:03:08.384Z"} 
[2025-09-02 21:04:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:04:08.506Z"} 
[2025-09-02 21:05:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:05:08.456Z"} 
[2025-09-02 21:06:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:06:08.293Z"} 
[2025-09-02 21:07:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:07:08.506Z"} 
[2025-09-02 21:08:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:08:08.307Z"} 
[2025-09-02 21:09:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:09:09.154Z"} 
[2025-09-02 21:10:08] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:10:08.235Z"} 
[2025-09-02 21:11:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:11:08.280Z"} 
[2025-09-02 21:12:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:12:08.294Z"} 
[2025-09-02 21:13:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:13:08.396Z"} 
[2025-09-02 21:14:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:14:09.749Z"} 
[2025-09-02 21:14:25] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:14:24.517Z"} 
[2025-09-02 21:15:08] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:15:08.244Z"} 
[2025-09-02 21:16:08] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:16:08.248Z"} 
[2025-09-02 21:17:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:17:08.544Z"} 
[2025-09-02 21:18:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:18:08.888Z"} 
[2025-09-02 21:19:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:19:08.416Z"} 
[2025-09-02 21:20:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:20:08.814Z"} 
[2025-09-02 21:21:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:21:08.294Z"} 
[2025-09-02 21:22:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:22:08.316Z"} 
[2025-09-02 21:23:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:23:08.336Z"} 
[2025-09-02 21:24:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:24:08.423Z"} 
[2025-09-02 21:25:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:25:08.323Z"} 
[2025-09-02 21:26:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:26:08.383Z"} 
[2025-09-02 21:27:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:27:08.317Z"} 
[2025-09-02 21:28:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:28:08.338Z"} 
[2025-09-02 21:29:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:29:08.846Z"} 
[2025-09-02 21:30:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:30:09.054Z"} 
[2025-09-02 21:31:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:31:08.851Z"} 
[2025-09-02 21:32:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:32:09.195Z"} 
[2025-09-02 21:33:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:33:09.199Z"} 
[2025-09-02 21:34:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:34:09.078Z"} 
[2025-09-02 21:35:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:35:09.232Z"} 
[2025-09-02 21:36:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:36:09.534Z"} 
[2025-09-02 21:37:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:37:09.162Z"} 
[2025-09-02 21:38:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:38:08.843Z"} 
[2025-09-02 21:39:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:39:09.088Z"} 
[2025-09-02 21:40:10] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:40:09.110Z"} 
[2025-09-02 21:41:02] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T21:41:02.128Z"} 
[2025-09-03 07:50:05] local.ERROR: Error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T07:50:03.392Z"} 
[2025-09-03 07:50:09] local.ERROR: Error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T07:50:07.538Z"} 
[2025-09-03 08:30:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/6","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:30:03.911Z"} 
[2025-09-03 08:31:07] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/6","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:31:05.805Z"} 
[2025-09-03 08:31:37] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/6","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:31:36.727Z"} 
[2025-09-03 08:32:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:32:02.884Z"} 
[2025-09-03 08:32:07] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/analytics","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:32:06.701Z"} 
[2025-09-03 08:32:38] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/analytics","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:32:37.378Z"} 
[2025-09-03 08:32:52] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:32:51.452Z"} 
[2025-09-03 08:33:23] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:33:21.742Z"} 
[2025-09-03 08:33:51] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:33:50.853Z"} 
[2025-09-03 08:34:23] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:34:21.590Z"} 
[2025-09-03 08:34:52] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:34:51.240Z"} 
[2025-09-03 08:35:21] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:35:21.058Z"} 
[2025-09-03 08:35:49] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:35:48.285Z"} 
[2025-09-03 08:35:52] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:35:50.959Z"} 
[2025-09-03 08:36:18] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:36:17.488Z"} 
[2025-09-03 08:36:21] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:36:20.486Z"} 
[2025-09-03 08:36:48] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:36:47.531Z"} 
[2025-09-03 08:36:52] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:36:50.761Z"} 
[2025-09-03 08:37:18] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:37:17.478Z"} 
[2025-09-03 08:37:21] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:37:20.423Z"} 
[2025-09-03 08:37:25] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:37:25.275Z"} 
[2025-09-03 08:37:56] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:37:55.449Z"} 
[2025-09-03 08:38:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:38:03.954Z"} 
[2025-09-03 08:38:25] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:38:25.164Z"} 
[2025-09-03 08:38:55] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:38:55.035Z"} 
[2025-09-03 08:39:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:39:03.537Z"} 
[2025-09-03 08:39:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:39:09.215Z"} 
[2025-09-03 08:39:39] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:39:39.227Z"} 
[2025-09-03 08:39:49] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:39:49.396Z"} 
[2025-09-03 08:40:08] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:40:05.679Z"} 
[2025-09-03 08:40:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:40:08.531Z"} 
[2025-09-03 08:40:34] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:40:33.620Z"} 
[2025-09-03 08:40:36] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:40:35.381Z"} 
[2025-09-03 08:40:52] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/analytics","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:40:50.081Z"} 
[2025-09-03 08:40:52] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/analytics","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:40:51.473Z"} 
[2025-09-03 08:41:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:41:04.140Z"} 
[2025-09-03 08:41:16] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:41:14.209Z"} 
[2025-09-03 08:41:16] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:41:15.641Z"} 
[2025-09-03 08:41:20] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:41:19.902Z"} 
[2025-09-03 08:41:22] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:41:21.448Z"} 
[2025-09-03 08:41:47] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/01990eb2-218b-73ee-9083-9815b30bc85f","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:41:46.595Z"} 
[2025-09-03 08:42:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:42:04.040Z"} 
[2025-09-03 08:42:17] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/01990eb2-218b-73ee-9083-9815b30bc85f","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:42:16.765Z"} 
[2025-09-03 08:42:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/01990eb2-218b-73ee-9083-9815b30bc85f","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:42:45.919Z"} 
[2025-09-03 08:43:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:43:04.109Z"} 
[2025-09-03 08:43:16] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/01990eb2-218b-73ee-9083-9815b30bc85f","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:43:16.284Z"} 
[2025-09-03 08:43:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users/01990eb2-218b-73ee-9083-9815b30bc85f","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:43:46.208Z"} 
[2025-09-03 08:44:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:44:03.938Z"} 
[2025-09-03 08:44:09] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/users/01990eb2-218b-73ee-9083-9815b30bc85f","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:44:09.146Z"} 
[2025-09-03 08:46:14] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:46:11.626Z"} 
[2025-09-03 08:46:31] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:46:31.465Z"} 
[2025-09-03 08:46:33] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:46:33.386Z"} 
[2025-09-03 08:46:47] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:46:47.250Z"} 
[2025-09-03 08:47:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:47:04.612Z"} 
[2025-09-03 08:47:06] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:47:05.501Z"} 
[2025-09-03 08:47:08] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:47:07.315Z"} 
[2025-09-03 08:47:15] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:47:14.191Z"} 
[2025-09-03 08:47:16] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:47:15.488Z"} 
[2025-09-03 08:47:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:47:45.625Z"} 
[2025-09-03 08:48:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:48:04.262Z"} 
[2025-09-03 08:48:16] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:48:16.295Z"} 
[2025-09-03 08:48:22] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:48:21.395Z"} 
[2025-09-03 08:48:45] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:48:45.060Z"} 
[2025-09-03 08:49:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:49:03.792Z"} 
[2025-09-03 08:49:15] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:49:15.362Z"} 
[2025-09-03 08:49:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:49:45.613Z"} 
[2025-09-03 08:50:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:50:03.466Z"} 
[2025-09-03 08:50:15] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:50:15.335Z"} 
[2025-09-03 08:50:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:50:45.646Z"} 
[2025-09-03 08:51:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:51:03.732Z"} 
[2025-09-03 08:51:16] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:51:15.559Z"} 
[2025-09-03 08:51:45] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:51:45.197Z"} 
[2025-09-03 08:52:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:52:03.460Z"} 
[2025-09-03 08:52:15] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:52:15.326Z"} 
[2025-09-03 08:52:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:52:45.552Z"} 
[2025-09-03 08:53:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:53:03.984Z"} 
[2025-09-03 08:53:15] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:53:15.417Z"} 
[2025-09-03 08:53:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:53:46.070Z"} 
[2025-09-03 08:54:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:54:04.274Z"} 
[2025-09-03 08:54:16] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:54:15.506Z"} 
[2025-09-03 08:54:45] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:54:45.429Z"} 
[2025-09-03 08:55:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:55:03.915Z"} 
[2025-09-03 08:55:17] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:55:16.923Z"} 
[2025-09-03 08:55:46] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:55:45.438Z"} 
[2025-09-03 08:56:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:56:07.809Z"} 
[2025-09-03 08:57:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:57:04.578Z"} 
[2025-09-03 08:58:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:58:03.811Z"} 
[2025-09-03 08:59:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T08:59:03.806Z"} 
[2025-09-03 09:00:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:00:03.974Z"} 
[2025-09-03 09:01:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:01:03.856Z"} 
[2025-09-03 09:02:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:02:03.918Z"} 
[2025-09-03 09:03:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:03:04.042Z"} 
[2025-09-03 09:04:07] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:04:05.605Z"} 
[2025-09-03 09:04:52] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:04:51.178Z"} 
[2025-09-03 09:05:20] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:05:18.728Z"} 
[2025-09-03 09:05:50] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:05:48.653Z"} 
[2025-09-03 09:06:20] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:06:19.350Z"} 
[2025-09-03 09:06:49] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:06:48.076Z"} 
[2025-09-03 09:07:19] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:07:18.151Z"} 
[2025-09-03 09:07:48] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:07:47.695Z"} 
[2025-09-03 09:08:18] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:08:18.100Z"} 
[2025-09-03 09:09:04] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:09:04.068Z"} 
[2025-09-03 09:10:05] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:10:04.155Z"} 
[2025-09-03 09:11:06] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:11:06.083Z"} 
[2025-09-03 09:12:06] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:12:05.900Z"} 
[2025-09-03 09:13:06] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:13:05.849Z"} 
[2025-09-03 09:13:39] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:13:38.112Z"} 
[2025-09-03 09:14:06] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:14:06.007Z"} 
[2025-09-03 09:15:06] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:15:05.709Z"} 
[2025-09-03 09:16:06] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:16:05.778Z"} 
[2025-09-03 09:17:07] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:17:05.842Z"} 
[2025-09-03 09:18:07] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:18:05.837Z"} 
[2025-09-03 09:19:07] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:19:05.914Z"} 
[2025-09-03 09:20:07] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:20:06.037Z"} 
[2025-09-03 09:29:22] local.ERROR: [vite] Failed to reload /resources/js/pages/membership.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://localhost:8000/membership","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:29:21.456Z"} 
[2025-09-03 09:30:20] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/membership.tsx?t=1756891761277 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/membership.tsx?t=1756891761277 {"url":"http://localhost:8000/membership","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:30:19.477Z"} 
[2025-09-03 09:30:28] local.ERROR: Unhandled Promise Rejection TypeError Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/membership.tsx?t=1756891821269 TypeError: Failed to fetch dynamically imported module: http://[::1]:5173/resources/js/pages/membership.tsx?t=1756891821269 {"url":"http://localhost:8000/membership","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:30:26.585Z"} 
[2025-09-03 10:19:29] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/membership","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T09:48:19.154Z"} 
[2025-09-03 17:46:09] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/membership","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T17:46:08.326Z"} 
[2025-09-03 17:48:28] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-03T17:48:26.832Z"} 
[2025-09-04 09:00:02] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:00:01.757Z"} 
[2025-09-04 09:00:59] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:00:58.193Z"} 
[2025-09-04 09:02:00] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:01:59.029Z"} 
[2025-09-04 09:02:59] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:02:58.436Z"} 
[2025-09-04 09:03:59] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:03:58.690Z"} 
[2025-09-04 09:04:59] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:04:58.135Z"} 
[2025-09-04 09:05:36] local.ERROR: Failed to fetch notifications: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:05:35.697Z"} 
[2025-09-04 09:16:00] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:15:59.008Z"} 
[2025-09-04 09:35:27] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:35:26.923Z"} 
[2025-09-04 09:35:44] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:35:44.121Z"} 
[2025-09-04 09:36:15] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:36:14.824Z"} 
[2025-09-04 09:37:27] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:37:27.571Z"} 
[2025-09-04 09:37:32] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:37:31.968Z"} 
[2025-09-04 09:37:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:37:55.722Z"} 
[2025-09-04 09:38:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:38:55.428Z"} 
[2025-09-04 09:39:50] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:39:50.096Z"} 
[2025-09-04 09:39:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:39:55.433Z"} 
[2025-09-04 09:40:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:40:55.377Z"} 
[2025-09-04 09:41:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:41:55.399Z"} 
[2025-09-04 09:42:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:42:55.568Z"} 
[2025-09-04 09:43:09] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:43:08.639Z"} 
[2025-09-04 09:43:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:43:55.366Z"} 
[2025-09-04 09:44:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:44:55.361Z"} 
[2025-09-04 09:45:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:45:55.421Z"} 
[2025-09-04 09:46:56] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZEGYC1gaQAPXqYn1DMtCw7GG0o4Rx7Sm0I198i8L get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T09:46:55.333Z"} 
[2025-09-04 09:47:09] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:47:05.972Z"} 
[2025-09-04 09:47:23] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:47:21.216Z"} 
[2025-09-04 09:47:32] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:47:31.696Z"} 
[2025-09-04 09:48:15] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:48:14.131Z"} 
[2025-09-04 09:48:24] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:48:23.584Z"} 
[2025-09-04 09:56:37] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users/01991416-4e7f-72df-8fb3-f81a2e42d94e","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:56:37.234Z"} 
[2025-09-04 09:57:38] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:57:37.824Z"} 
[2025-09-04 09:57:52] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:57:49.334Z"} 
[2025-09-04 09:57:56] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:57:55.578Z"} 
[2025-09-04 09:59:27] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/admin/users/01991416-4e7f-72df-8fb3-f81a2e42d94e","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T09:59:22.056Z"} 
[2025-09-04 10:00:16] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest LwYmabR1JtuSvJh9dnP6JYMqOkZq8zh3xIFWJ1I9 get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T10:00:16.173Z"} 
[2025-09-04 10:00:20] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T10:00:19.936Z"} 
[2025-09-04 10:06:50] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/users/0199142b-618c-72f1-b988-79a9cee52f46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-04T10:06:50.113Z"} 
[2025-09-04 10:06:51] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-04T10:06:50.113Z"} 
[2025-09-06 07:55:34] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/admin/analytics","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T07:55:32.904Z"} 
[2025-09-06 08:02:31] local.DEBUG: Payment cancelled {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:02:31.025Z"} 
[2025-09-06 08:12:59] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:12:58.389Z"} 
[2025-09-06 08:13:58] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:13:58.037Z"} 
[2025-09-06 08:14:58] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:14:58.067Z"} 
[2025-09-06 08:15:59] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:15:58.411Z"} 
[2025-09-06 08:16:58] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:16:58.153Z"} 
[2025-09-06 08:17:58] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:17:58.043Z"} 
[2025-09-06 08:18:59] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:18:58.389Z"} 
[2025-09-06 08:19:58] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:19:58.197Z"} 
[2025-09-06 08:20:58] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:20:58.350Z"} 
[2025-09-06 08:21:58] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:21:57.994Z"} 
[2025-09-06 08:22:59] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:22:58.448Z"} 
[2025-09-06 08:23:59] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest nmVxES729Tgf3PXDo5R9lcm5iLqMeXSvbtVoncDG get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:23:58.576Z"} 
[2025-09-06 08:45:07] local.ERROR: Error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:45:02.395Z"} 
[2025-09-06 08:45:22] local.ERROR: Error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-06T08:45:20.155Z"} 
[2025-09-06 08:52:01] local.DEBUG: Payment cancelled {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-06T08:52:00.662Z"} 
[2025-09-06 08:53:35] local.DEBUG: Payment cancelled {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:53:35.323Z"} 
[2025-09-06 08:55:44] local.ERROR: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401
    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)
    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest mDhaBu2tZjW15U3hw3KjTKdhDECJH8RgAze3SQmT get /notifications/dropdown true ERR_BAD_REQUEST 401 {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:55:43.033Z"} 
[2025-09-06 08:58:07] local.ERROR: Error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:58:05.461Z"} 
[2025-09-06 08:59:01] local.ERROR: Failed to fetch notifications: Network Error AxiosError AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)
    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest 3gH0V8n45B0J0NsopZz5uNCA3oRFF3M2I2UQjag6 get /notifications/dropdown true ERR_NETWORK {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:58:58.111Z"} 
[2025-09-06 08:59:05] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T08:59:05.369Z"} 
[2025-09-06 09:00:45] local.DEBUG: Payment cancelled {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T09:00:44.466Z"} 
[2025-09-06 09:40:17] local.DEBUG: Initializing payment with route: http://localhost:8000/wallet/deposit/initialize {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T09:40:15.786Z"} 
[2025-09-06 09:40:35] local.DEBUG: Payment cancelled {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-06T09:40:35.153Z"} 
