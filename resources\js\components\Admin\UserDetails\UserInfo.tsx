import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Star, User as UserIcon } from 'lucide-react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number;
    phone?: string;
    location?: string;
    bio?: string;
    skills?: string[];
    education?: string;
    created_at: string;
    projects: any[];
    bids: any[];
    wallet_transactions: any[];
}

interface UserStats {
    total_projects: number;
    total_bids: number;
    total_spent: number;
    total_earned: number;
    average_rating: number;
    total_ratings: number;
}

interface UserInfoProps {
    user: User;
    user_stats: UserStats;
}

export default function UserInfo({ user, user_stats }: UserInfoProps) {
    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <UserIcon className="h-5 w-5" />
                        <span>Personal Information</span>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Name</p>
                            <p className="text-sm">{user.name}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Email</p>
                            <p className="text-sm">{user.email}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Phone</p>
                            <p className="text-sm">{user.phone || 'Not provided'}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Location</p>
                            <p className="text-sm">{user.location || 'Not provided'}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Education</p>
                            <p className="text-sm">{user.education || 'Not provided'}</p>
                        </div>
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Member Since</p>
                            <p className="text-sm">{new Date(user.created_at).toLocaleDateString()}</p>
                        </div>
                    </div>
                    {user.bio && (
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Bio</p>
                            <p className="text-sm">{user.bio}</p>
                        </div>
                    )}
                    {user.skills && user.skills.length > 0 && (
                        <div>
                            <p className="text-sm font-medium text-muted-foreground">Skills</p>
                            <div className="mt-2 flex flex-wrap gap-2">
                                {user.skills.map((skill, index) => (
                                    <Badge key={index} variant="secondary">
                                        {skill}
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* User Statistics */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <Star className="h-5 w-5" />
                        <span>Statistics</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                        <div className="text-center">
                            <p className="text-2xl font-bold text-primary">{user_stats.total_projects}</p>
                            <p className="text-sm text-muted-foreground">Projects</p>
                        </div>
                        <div className="text-center">
                            <p className="text-2xl font-bold text-primary">{user_stats.total_bids}</p>
                            <p className="text-sm text-muted-foreground">Bids</p>
                        </div>
                        <div className="text-center">
                            <p className="text-2xl font-bold text-primary">₵{user_stats.total_spent}</p>
                            <p className="text-sm text-muted-foreground">Spent</p>
                        </div>
                        <div className="text-center">
                            <p className="text-2xl font-bold text-primary">₵{user_stats.total_earned}</p>
                            <p className="text-sm text-muted-foreground">Earned</p>
                        </div>
                    </div>
                    <div className="mt-4 text-center">
                        <p className="text-lg font-semibold">{user_stats.average_rating.toFixed(1)} ⭐</p>
                        <p className="text-sm text-muted-foreground">
                            ({user_stats.total_ratings} rating{user_stats.total_ratings !== 1 ? 's' : ''})
                        </p>
                    </div>
                </CardContent>
            </Card>
        </>
    );
}
