import ActivityFeed from '@/components/Admin/Analytics/ActivityFeed';
import CommissionOverview from '@/components/Admin/Analytics/CommissionOverview';
import DateFilter from '@/components/Admin/Analytics/DateFilter';
import DetailedStats from '@/components/Admin/Analytics/DetailedStats';
import HealthMetrics from '@/components/Admin/Analytics/HealthMetrics';
import MetricsCards from '@/components/Admin/Analytics/MetricsCards';
import AppLayout from '@/layouts/app-layout';
import { Head, router, useForm } from '@inertiajs/react';
import { BarChart3 } from 'lucide-react';

interface UserStats {
    total_users: number;
    new_users_this_month: number;
    active_users: number;
    verified_users: number;
    users_by_role: Record<string, number>;
}

interface ProjectStats {
    total_projects: number;
    open_projects: number;
    in_progress_projects: number;
    completed_projects: number;
    projects_by_category: Record<string, number>;
    projects_by_academic_level: Record<string, number>;
}

interface FinancialStats {
    total_wallet_balance: number;
    total_transactions: number;
    total_deposits: number;
    total_withdrawals: number;
    pending_withdrawals: number;
    // Platform Commission data
    total_commission_earned?: number;
    pending_commission?: number;
    commission_withdrawn?: number;
    available_commission?: number;
    revenue_by_month: Array<{
        month: string;
        deposits: number;
        withdrawals: number;
    }>;
    commission_by_month?: Array<{
        month: string;
        total_commission: number;
    }>;
}

interface BidStats {
    total_bids: number;
    accepted_bids: number;
    pending_bids: number;
    rejected_bids: number;
    average_bid_amount?: number;
}

interface RecentActivity {
    recent_users: Array<{
        id: string;
        name: string;
        email: string;
        created_at: string;
        role: string;
    }>;
    recent_projects: Array<{
        id: string;
        title: string;
        user_id: string;
        status: string;
        created_at: string;
        user: {
            id: string;
            name: string;
        };
    }>;
    recent_transactions?: Array<{
        id: string;
        user_id: string;
        type: string;
        amount: number;
        status: string;
        created_at: string;
        user: {
            id: string;
            name: string;
        };
    }>;
    recent_commissions?: Array<{
        id: string;
        project_id: string;
        milestone_id?: string;
        amount: number;
        status: string;
        collected_at: string;
        project: {
            id: string;
            title: string;
        };
        milestone?: {
            id: string;
            title: string;
        };
    }>;
}
interface HealthMetrics {
    project_completion_rate: number;
    user_verification_rate: number;
    bid_acceptance_rate: number;
    average_project_value?: number;
}

interface Props {
    userStats: UserStats;
    projectStats: ProjectStats;
    financialStats: FinancialStats;
    bidStats: BidStats;
    recentActivity: RecentActivity;
    healthMetrics: HealthMetrics;
    permissions: {
        can_view_financial_data: boolean;
    };
    filters: {
        start_date: string;
        end_date: string;
    };
}

export default function Analytics({ userStats, projectStats, financialStats, bidStats, recentActivity, healthMetrics, permissions, filters }: Props) {
    const { data, setData } = useForm({
        start_date: filters.start_date,
        end_date: filters.end_date,
    });

    const handleFilterChange = () => {
        router.get(route('admin.analytics.index'), data, {
            preserveState: true,
            replace: true,
        });
    };

    const formatCurrency = (amount: number) => `₵${Number(amount || 0).toFixed(2)}`;
    const formatPercentage = (value: number) => `${Number(value || 0).toFixed(1)}%`;

    return (
        <AppLayout>
            <Head title="Analytics Dashboard" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <h1 className="flex flex-col gap-2 text-3xl font-bold text-gray-900 sm:flex-row sm:items-center">
                            <BarChart3 className="h-8 w-8 text-blue-600" />
                            Analytics Dashboard
                        </h1>
                        <p className="mt-2 text-gray-600">Platform insights and performance metrics</p>
                    </div>

                    {/* Date Filter */}
                    <DateFilter data={data} setData={setData} onFilterChange={handleFilterChange} />

                    {/* Key Metrics */}
                    <MetricsCards
                        userStats={userStats}
                        projectStats={projectStats}
                        financialStats={financialStats}
                        bidStats={bidStats}
                        permissions={permissions}
                        formatCurrency={formatCurrency}
                    />

                    {/* Health Metrics */}
                    <HealthMetrics
                        healthMetrics={healthMetrics}
                        permissions={permissions}
                        formatPercentage={formatPercentage}
                        formatCurrency={formatCurrency}
                    />

                    {/* Platform Commission Overview */}
                    <CommissionOverview financialStats={financialStats} permissions={permissions} formatCurrency={formatCurrency} />

                    {/* Detailed Statistics */}
                    <DetailedStats userStats={userStats} projectStats={projectStats} />

                    {/* Recent Activity */}
                    <ActivityFeed recentActivity={recentActivity} permissions={permissions} formatCurrency={formatCurrency} />
                </div>
            </div>
        </AppLayout>
    );
}
