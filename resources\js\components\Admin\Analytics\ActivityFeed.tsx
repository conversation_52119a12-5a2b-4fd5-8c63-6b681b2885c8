import { Badge } from '@/components/ui/badge';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Activity } from 'lucide-react';

interface RecentActivity {
    recent_users: Array<{
        id: string;
        name: string;
        email: string;
        created_at: string;
        role: string;
    }>;
    recent_projects: Array<{
        id: string;
        title: string;
        user_id: string;
        status: string;
        created_at: string;
        user: {
            id: string;
            name: string;
        };
    }>;
    recent_transactions?: Array<{
        id: string;
        user_id: string;
        type: string;
        amount: number;
        status: string;
        created_at: string;
        user: {
            id: string;
            name: string;
        };
    }>;
    recent_commissions?: Array<{
        id: string;
        project_id: string;
        milestone_id?: string;
        amount: number;
        status: string;
        collected_at: string;
        project: {
            id: string;
            title: string;
        };
        milestone?: {
            id: string;
            title: string;
        };
    }>;
}

interface ActivityFeedProps {
    recentActivity: RecentActivity;
    permissions: {
        can_view_financial_data: boolean;
    };
    formatCurrency: (amount: number) => string;
}

export default function ActivityFeed({ recentActivity, permissions, formatCurrency }: ActivityFeedProps) {
    return (
        <div className="mt-8">
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5" />
                        Recent Activity
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
                        {/* Recent Users */}
                        <div>
                            <h4 className="mb-3 font-medium">Recent Users</h4>
                            <div className="space-y-2">
                                {recentActivity.recent_users.map((user) => (
                                    <div
                                        key={user.id}
                                        className="flex flex-col gap-2 rounded-lg bg-gray-50 p-3 sm:flex-row sm:items-center sm:justify-between"
                                    >
                                        <div className="min-w-0 flex-1">
                                            <p className="truncate text-sm font-medium">{user.name}</p>
                                            <p className="text-xs text-gray-500">{user.role}</p>
                                        </div>
                                        <Badge variant="outline" className="text-xs">
                                            {new Date(user.created_at).toLocaleDateString()}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Recent Projects */}
                        <div>
                            <h4 className="mb-3 font-medium">Recent Projects</h4>
                            <div className="space-y-2">
                                {recentActivity.recent_projects.map((project) => (
                                    <div key={project.id} className="rounded-lg bg-gray-50 p-3">
                                        <p className="truncate text-sm font-medium">{project.title}</p>
                                        <p className="text-xs text-gray-500">by {project.user.name}</p>
                                        <div className="mt-2 flex flex-col gap-1 sm:flex-row sm:justify-between">
                                            <Badge variant="outline" className="w-fit text-xs">
                                                {project.status}
                                            </Badge>
                                            <span className="text-xs text-gray-400">{new Date(project.created_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Recent Transactions - Only for super admins */}
                        {permissions.can_view_financial_data && recentActivity.recent_transactions && (
                            <div className="md:col-span-2 xl:col-span-1">
                                <h4 className="mb-3 font-medium">Recent Transactions</h4>
                                <div className="space-y-2">
                                    {recentActivity.recent_transactions.slice(0, 5).map((transaction) => (
                                        <div key={transaction.id} className="rounded-lg bg-gray-50 p-3">
                                            <div className="flex flex-col gap-2 sm:flex-row sm:justify-between">
                                                <p className="text-sm font-medium">{transaction.user.name}</p>
                                                <span
                                                    className={`text-sm font-medium ${
                                                        transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                                                    }`}
                                                >
                                                    {transaction.type === 'deposit' ? '+' : '-'}
                                                    {formatCurrency(transaction.amount)}
                                                </span>
                                            </div>
                                            <div className="flex flex-col gap-1 sm:flex-row sm:justify-between">
                                                <Badge variant="outline" className="w-fit text-xs">
                                                    {transaction.type}
                                                </Badge>
                                                <span className="text-xs text-gray-400">{new Date(transaction.created_at).toLocaleDateString()}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Recent Commissions - Only for super admins */}
                        {permissions.can_view_financial_data && recentActivity.recent_commissions && (
                            <div className="md:col-span-2 xl:col-span-1">
                                <h4 className="mb-3 font-medium">Recent Commissions</h4>
                                <div className="space-y-2">
                                    {recentActivity.recent_commissions.slice(0, 5).map((commission) => (
                                        <div key={commission.id} className="rounded-lg bg-gray-50 p-3">
                                            <div className="flex flex-col gap-2 sm:flex-row sm:justify-between">
                                                <p className="truncate text-sm font-medium">{commission.project.title}</p>
                                                <span className="text-sm font-medium text-green-600">+{formatCurrency(commission.amount)}</span>
                                            </div>
                                            <div className="flex flex-col gap-1 sm:flex-row sm:justify-between">
                                                <Badge variant="outline" className="w-fit text-xs">
                                                    {commission.status}
                                                </Badge>
                                                <span className="text-xs text-gray-400">
                                                    {commission.collected_at ? new Date(commission.collected_at).toLocaleDateString() : 'Pending'}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
