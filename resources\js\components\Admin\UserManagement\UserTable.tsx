import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Link, router } from '@inertiajs/react';
import { Ban, CheckCircle, Eye, ShieldCheck, Trash2, UserCheck, UserX, Wallet, XCircle } from 'lucide-react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number | string;
    phone?: string;
    location?: string;
    created_at: string;
    projects_count: number;
    bids_count: number;
}

interface UserTableProps {
    users: {
        data: User[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    searchForm: {
        data: {
            search: string;
            role: string;
            status: string;
            verified: string;
        };
    };
    permissions: {
        can_delete_users: boolean;
        can_view_financial_data: boolean;
    };
    authUser: User;
    onStatusUpdate: (user: User, status: string) => void;
    onVerificationUpdate: (user: User, verified: boolean) => void;
    onWalletAdjustment: (user: User) => void;
    onDelete: (user: User) => void;
}

export default function UserTable({
    users,
    searchForm,
    permissions,
    authUser,
    onStatusUpdate,
    onVerificationUpdate,
    onWalletAdjustment,
    onDelete,
}: UserTableProps) {
    const getStatusBadge = (status: string) => {
        const variants = {
            active: 'bg-green-100 text-green-800',
            suspended: 'bg-yellow-100 text-yellow-800',
            blocked: 'bg-red-100 text-red-800',
        };
        return variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800';
    };

    const getRoleBadge = (role: string) => {
        const variants = {
            user: 'bg-blue-100 text-blue-800',
            admin: 'bg-purple-100 text-purple-800',
            super_admin: 'bg-red-100 text-red-800',
        };
        return variants[role as keyof typeof variants] || 'bg-gray-100 text-gray-800';
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Users ({users.total})</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">User</th>
                                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">Role & Status</th>
                                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">Wallet</th>
                                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">Activity</th>
                                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                            {users.data.map((user) => (
                                <tr key={user.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div className="flex items-center gap-2">
                                                <div className="text-sm font-medium text-gray-900">{user.name}</div>
                                                {user.is_verified && <ShieldCheck className="h-4 w-4 text-green-500" />}
                                            </div>
                                            <div className="text-sm text-gray-500">{user.email}</div>
                                            {user.phone && <div className="text-xs text-gray-400">{user.phone}</div>}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="space-y-1">
                                            <Badge className={getRoleBadge(user.role)}>{user.role.replace('_', ' ').toUpperCase()}</Badge>
                                            <Badge className={getStatusBadge(user.status)}>{user.status.toUpperCase()}</Badge>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">₵{Number(user.wallet_balance || 0).toFixed(2)}</div>
                                    </td>
                                    <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500">
                                        <div>{user.projects_count} projects</div>
                                        <div>{user.bids_count} bids</div>
                                    </td>
                                    <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm">
                                                    Actions
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuLabel>User Actions</DropdownMenuLabel>
                                                <DropdownMenuSeparator />

                                                <DropdownMenuItem asChild>
                                                    <Link href={route('admin.users.show', user.id)}>
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        View Details
                                                    </Link>
                                                </DropdownMenuItem>

                                                {user.status !== 'active' && (
                                                    <DropdownMenuItem onClick={() => onStatusUpdate(user, 'active')}>
                                                        <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                                        Activate
                                                    </DropdownMenuItem>
                                                )}

                                                {user.status !== 'suspended' && (
                                                    <DropdownMenuItem onClick={() => onStatusUpdate(user, 'suspended')}>
                                                        <Ban className="mr-2 h-4 w-4 text-yellow-600" />
                                                        Suspend
                                                    </DropdownMenuItem>
                                                )}

                                                {user.status !== 'blocked' && (
                                                    <DropdownMenuItem onClick={() => onStatusUpdate(user, 'blocked')}>
                                                        <XCircle className="mr-2 h-4 w-4 text-red-600" />
                                                        Block
                                                    </DropdownMenuItem>
                                                )}

                                                <DropdownMenuSeparator />

                                                <DropdownMenuItem onClick={() => onVerificationUpdate(user, !user.is_verified)}>
                                                    {user.is_verified ? (
                                                        <>
                                                            <UserX className="mr-2 h-4 w-4 text-orange-600" />
                                                            Remove Verification
                                                        </>
                                                    ) : (
                                                        <>
                                                            <UserCheck className="mr-2 h-4 w-4 text-green-600" />
                                                            Verify User
                                                        </>
                                                    )}
                                                </DropdownMenuItem>

                                                <DropdownMenuItem onClick={() => onWalletAdjustment(user)}>
                                                    <Wallet className="mr-2 h-4 w-4 text-blue-600" />
                                                    Adjust Wallet
                                                </DropdownMenuItem>

                                                {user.role !== 'super_admin' && permissions.can_delete_users && authUser.role === 'super_admin' && (
                                                    <>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem onClick={() => onDelete(user)} className="text-red-600">
                                                            <Trash2 className="mr-2 h-4 w-4" />
                                                            Delete User
                                                        </DropdownMenuItem>
                                                    </>
                                                )}
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                {users.last_page > 1 && (
                    <div className="mt-6 flex justify-center">
                        <div className="flex space-x-2">
                            {Array.from({ length: users.last_page }, (_, i) => i + 1).map((page) => (
                                <Button
                                    key={page}
                                    variant={page === users.current_page ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() =>
                                        router.get(route('admin.users.index'), {
                                            ...searchForm.data,
                                            page,
                                        })
                                    }
                                >
                                    {page}
                                </Button>
                            ))}
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
