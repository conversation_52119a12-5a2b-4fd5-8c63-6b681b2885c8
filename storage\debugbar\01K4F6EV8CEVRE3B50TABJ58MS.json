{"__meta": {"id": "01K4F6EV8CEVRE3B50TABJ58MS", "datetime": "2025-09-06 09:41:58", "utime": **********.669949, "method": "GET", "uri": "/admin/analytics", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.089055, "end": **********.669973, "duration": 0.5809178352355957, "duration_str": "581ms", "measures": [{"label": "Booting", "start": **********.089055, "relative_start": 0, "end": **********.261749, "relative_end": **********.261749, "duration": 0.*****************, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.261762, "relative_start": 0.*****************, "end": **********.669976, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.279013, "relative_start": 0.***************, "end": **********.281449, "relative_end": **********.281449, "duration": 0.0024361610412597656, "duration_str": "2.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.638687, "relative_start": 0.****************, "end": **********.666678, "relative_end": **********.666678, "duration": 0.027991056442260742, "duration_str": "27.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Admin/Analytics", "param_count": null, "params": [], "start": **********.669801, "type": "tsx", "hash": "tsxC:\\dev\\thesylink\\resources\\js/Pages/Admin/Analytics.tsxAdmin/Analytics", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fresources%2Fjs%2Fpages%2FAdmin%2FAnalytics.tsx&line=1", "ajax": false, "filename": "Analytics.tsx", "line": "?"}}]}, "queries": {"count": 39, "nb_statements": 38, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.24722000000000002, "accumulated_duration_str": "247ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.294753, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy' limit 1", "type": "query", "params": [], "bindings": ["MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.300484, "duration": 0.07287, "duration_str": "72.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 29.476}, {"sql": "select * from \"users\" where \"id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' limit 1", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.385223, "duration": 0.008230000000000001, "duration_str": "8.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 29.476, "width_percent": 3.329}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.399869, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:31", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=31", "ajax": false, "filename": "AnalyticsController.php", "line": "31"}, "connection": "thesylink", "explain": null, "start_percent": 32.805, "width_percent": 1.485}, {"sql": "select count(*) as aggregate from \"users\" where extract(month from \"created_at\") = '09'", "type": "query", "params": [], "bindings": ["09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.404985, "duration": 0.00991, "duration_str": "9.91ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:32", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=32", "ajax": false, "filename": "AnalyticsController.php", "line": "32"}, "connection": "thesylink", "explain": null, "start_percent": 34.289, "width_percent": 4.009}, {"sql": "select count(*) as aggregate from \"users\" where \"status\" = 'active'", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.4164371, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:33", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=33", "ajax": false, "filename": "AnalyticsController.php", "line": "33"}, "connection": "thesylink", "explain": null, "start_percent": 38.298, "width_percent": 1.258}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.421446, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:34", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=34", "ajax": false, "filename": "AnalyticsController.php", "line": "34"}, "connection": "thesylink", "explain": null, "start_percent": 39.556, "width_percent": 1.319}, {"sql": "select \"role\", count(*) as count from \"users\" group by \"role\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 37}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.428545, "duration": 0.00855, "duration_str": "8.55ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:37", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=37", "ajax": false, "filename": "AnalyticsController.php", "line": "37"}, "connection": "thesylink", "explain": null, "start_percent": 40.875, "width_percent": 3.458}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.4401581, "duration": 0.01051, "duration_str": "10.51ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:43", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=43", "ajax": false, "filename": "AnalyticsController.php", "line": "43"}, "connection": "thesylink", "explain": null, "start_percent": 44.333, "width_percent": 4.251}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'open'", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.4522471, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:44", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=44", "ajax": false, "filename": "AnalyticsController.php", "line": "44"}, "connection": "thesylink", "explain": null, "start_percent": 48.584, "width_percent": 1.112}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'in_progress'", "type": "query", "params": [], "bindings": ["in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.456741, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=45", "ajax": false, "filename": "AnalyticsController.php", "line": "45"}, "connection": "thesylink", "explain": null, "start_percent": 49.697, "width_percent": 1.133}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.461223, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:46", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=46", "ajax": false, "filename": "AnalyticsController.php", "line": "46"}, "connection": "thesylink", "explain": null, "start_percent": 50.829, "width_percent": 0.955}, {"sql": "select \"category\", count(*) as count from \"projects\" where \"category\" is not null group by \"category\" order by \"count\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 52}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.464873, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:52", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=52", "ajax": false, "filename": "AnalyticsController.php", "line": "52"}, "connection": "thesylink", "explain": null, "start_percent": 51.784, "width_percent": 1.707}, {"sql": "select \"academic_level\", count(*) as count from \"projects\" where \"academic_level\" is not null group by \"academic_level\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.471038, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:57", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=57", "ajax": false, "filename": "AnalyticsController.php", "line": "57"}, "connection": "thesylink", "explain": null, "start_percent": 53.491, "width_percent": 1.351}, {"sql": "select sum(\"wallet_balance\") as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.476037, "duration": 0.00735, "duration_str": "7.35ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:65", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=65", "ajax": false, "filename": "AnalyticsController.php", "line": "65"}, "connection": "thesylink", "explain": null, "start_percent": 54.842, "width_percent": 2.973}, {"sql": "select count(*) as aggregate from \"wallet_transactions\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.485059, "duration": 0.008039999999999999, "duration_str": "8.04ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:66", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=66", "ajax": false, "filename": "AnalyticsController.php", "line": "66"}, "connection": "thesylink", "explain": null, "start_percent": 57.815, "width_percent": 3.252}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'deposit' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["deposit", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.494323, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:67", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=67", "ajax": false, "filename": "AnalyticsController.php", "line": "67"}, "connection": "thesylink", "explain": null, "start_percent": 61.067, "width_percent": 1.189}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["withdrawal", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.4984698, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=68", "ajax": false, "filename": "AnalyticsController.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 62.256, "width_percent": 1.023}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["withdrawal", "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5022008, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:69", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=69", "ajax": false, "filename": "AnalyticsController.php", "line": "69"}, "connection": "thesylink", "explain": null, "start_percent": 63.28, "width_percent": 1.011}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END) as deposits, SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END) as withdrawals from \"wallet_transactions\" where extract(year from \"created_at\") = 2025 group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": [2025], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 78}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5064628, "duration": 0.01132, "duration_str": "11.32ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:78", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=78", "ajax": false, "filename": "AnalyticsController.php", "line": "78"}, "connection": "thesylink", "explain": null, "start_percent": 64.291, "width_percent": 4.579}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 85}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.523866, "duration": 0.01334, "duration_str": "13.34ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:85", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=85", "ajax": false, "filename": "AnalyticsController.php", "line": "85"}, "connection": "thesylink", "explain": null, "start_percent": 68.87, "width_percent": 5.396}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.538954, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:86", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=86", "ajax": false, "filename": "AnalyticsController.php", "line": "86"}, "connection": "thesylink", "explain": null, "start_percent": 74.266, "width_percent": 2.172}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 87}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.546118, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:87", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=87", "ajax": false, "filename": "AnalyticsController.php", "line": "87"}, "connection": "thesylink", "explain": null, "start_percent": 76.438, "width_percent": 1.485}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'rejected'", "type": "query", "params": [], "bindings": ["rejected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 88}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.551496, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:88", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=88", "ajax": false, "filename": "AnalyticsController.php", "line": "88"}, "connection": "thesylink", "explain": null, "start_percent": 77.922, "width_percent": 1.501}, {"sql": "select avg(\"amount\") as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5574338, "duration": 0.00533, "duration_str": "5.33ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:93", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=93", "ajax": false, "filename": "AnalyticsController.php", "line": "93"}, "connection": "thesylink", "explain": null, "start_percent": 79.423, "width_percent": 2.156}, {"sql": "select \"id\", \"name\", \"email\", \"created_at\", \"role\" from \"users\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 98}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.564728, "duration": 0.00511, "duration_str": "5.11ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:98", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=98", "ajax": false, "filename": "AnalyticsController.php", "line": "98"}, "connection": "thesylink", "explain": null, "start_percent": 81.579, "width_percent": 2.067}, {"sql": "select \"id\", \"title\", \"user_id\", \"status\", \"created_at\" from \"projects\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.571826, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:99", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=99", "ajax": false, "filename": "AnalyticsController.php", "line": "99"}, "connection": "thesylink", "explain": null, "start_percent": 83.646, "width_percent": 1.683}, {"sql": "select \"id\", \"name\" from \"users\" where \"users\".\"id\" in ('01991e3e-2cd2-7050-ae72-4e0beef93fb6', '01991e3e-2dde-736d-982b-0cac6a8d8141', '01991e3e-2f04-704f-b98a-ab1d99f5cc76')", "type": "query", "params": [], "bindings": ["01991e3e-2cd2-7050-ae72-4e0beef93fb6", "01991e3e-2dde-736d-982b-0cac6a8d8141", "01991e3e-2f04-704f-b98a-ab1d99f5cc76"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.584195, "duration": 0.00598, "duration_str": "5.98ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:99", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=99", "ajax": false, "filename": "AnalyticsController.php", "line": "99"}, "connection": "thesylink", "explain": null, "start_percent": 85.329, "width_percent": 2.419}, {"sql": "select \"id\", \"user_id\", \"type\", \"amount\", \"status\", \"created_at\" from \"wallet_transactions\" order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.592315, "duration": 0.0051600000000000005, "duration_str": "5.16ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:107", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=107", "ajax": false, "filename": "AnalyticsController.php", "line": "107"}, "connection": "thesylink", "explain": null, "start_percent": 87.748, "width_percent": 2.087}, {"sql": "select \"id\", \"name\" from \"users\" where \"users\".\"id\" in ('01991e3e-2ac7-73a5-b86e-ba67f54988d5')", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.599352, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:107", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=107", "ajax": false, "filename": "AnalyticsController.php", "line": "107"}, "connection": "thesylink", "explain": null, "start_percent": 89.835, "width_percent": 1.068}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"users\" where \"created_at\" >= '2024-09-06 09:41:58' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 09:41:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.603668, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:119", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=119", "ajax": false, "filename": "AnalyticsController.php", "line": "119"}, "connection": "thesylink", "explain": null, "start_percent": 90.903, "width_percent": 1.472}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"projects\" where \"created_at\" >= '2024-09-06 09:41:58' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 09:41:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 128}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.609733, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:128", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=128", "ajax": false, "filename": "AnalyticsController.php", "line": "128"}, "connection": "thesylink", "explain": null, "start_percent": 92.375, "width_percent": 1.185}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.6140618, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:134", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=134", "ajax": false, "filename": "AnalyticsController.php", "line": "134"}, "connection": "thesylink", "explain": null, "start_percent": 93.56, "width_percent": 0.951}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.617454, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:134", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=134", "ajax": false, "filename": "AnalyticsController.php", "line": "134"}, "connection": "thesylink", "explain": null, "start_percent": 94.511, "width_percent": 0.89}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.6206222, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:135", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=135", "ajax": false, "filename": "AnalyticsController.php", "line": "135"}, "connection": "thesylink", "explain": null, "start_percent": 95.401, "width_percent": 1.031}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.624614, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:135", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=135", "ajax": false, "filename": "AnalyticsController.php", "line": "135"}, "connection": "thesylink", "explain": null, "start_percent": 96.432, "width_percent": 1.036}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.628153, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:136", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=136", "ajax": false, "filename": "AnalyticsController.php", "line": "136"}, "connection": "thesylink", "explain": null, "start_percent": 97.468, "width_percent": 0.862}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.631232, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:136", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=136", "ajax": false, "filename": "AnalyticsController.php", "line": "136"}, "connection": "thesylink", "explain": null, "start_percent": 98.329, "width_percent": 0.837}, {"sql": "select avg(\"accepted_bid_amount\") as aggregate from \"projects\" where \"accepted_bid_amount\" is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 141}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.634234, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:141", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 141}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=141", "ajax": false, "filename": "AnalyticsController.php", "line": "141"}, "connection": "thesylink", "explain": null, "start_percent": 99.167, "width_percent": 0.833}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Project": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\WalletTransaction": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FWalletTransaction.php&line=1", "ajax": false, "filename": "WalletTransaction.php", "line": "?"}}}, "count": 20, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 20}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index", "uri": "GET admin/analytics", "controller": "App\\Http\\Controllers\\Admin\\AnalyticsController@index<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/AnalyticsController.php:20-160</a>", "middleware": "web, auth, verified, admin", "duration": "586ms", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1753295534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1753295534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2121970047 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2121970047\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1749025217 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InpEbEZqQXJQajFGZUlMcXI4SGY2Z2c9PSIsInZhbHVlIjoiQW9WeU1NMVl1VUhoTkdPOVpkaEhCYlpwRmNka1lRZGEzUklJajJhSi8rM0JOV0Q5R280V1Y2eE0yN0hZckp5aXJyKy9LVnNUNW1PcGVxdUNheE0rU21QMEUzYkhDNWFkR0NjUXJabjU4UytITTcxWGJrZlZrK002Z242TnUvSWIiLCJtYWMiOiI2YmNkZWRkNjhlZjNkMzAzNTkwNzE1NjYzZmNmNjgzZmE0NWU1MjU2OTFiZjc4ZDk4ODg0ZWQyMDVhMzMwMzU2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/wallet/add-funds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6InpEbEZqQXJQajFGZUlMcXI4SGY2Z2c9PSIsInZhbHVlIjoiQW9WeU1NMVl1VUhoTkdPOVpkaEhCYlpwRmNka1lRZGEzUklJajJhSi8rM0JOV0Q5R280V1Y2eE0yN0hZckp5aXJyKy9LVnNUNW1PcGVxdUNheE0rU21QMEUzYkhDNWFkR0NjUXJabjU4UytITTcxWGJrZlZrK002Z242TnUvSWIiLCJtYWMiOiI2YmNkZWRkNjhlZjNkMzAzNTkwNzE1NjYzZmNmNjgzZmE0NWU1MjU2OTFiZjc4ZDk4ODg0ZWQyMDVhMzMwMzU2IiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6Ilp0dStvK0M3dk9lSUg5enhFRlp6V0E9PSIsInZhbHVlIjoiZWo1dUJSaW5FVVhPazJrWDdLTDlWRHZrVVdmZm54WVczc3ZpWGd1Y3QwOE1FRW9zVFNBNTAybVhDZU9vd2h1RUVuRUxxY0IvNDR2OExyT3I3aGtpWDlTZjRkck9NZFQ1Y3Uvd01OdWJFSHRZTjkyazVpRlRqeDVhUWFzMVlINGYiLCJtYWMiOiI5NDM0NDEyZDY2MTdmOTNhZDhmOWY4NjA1MjhkMWNiMmM2OTE5MWE5ZWEyMDQ1YmUxMTczMjQxYWY1NmYzMWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749025217\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1726756380 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726756380\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1568855110 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 09:41:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568855110\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-747325871 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/wallet/add-funds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">01991e3e-2ac7-73a5-b86e-ba67f54988d5</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747325871\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index"}, "badge": null}}