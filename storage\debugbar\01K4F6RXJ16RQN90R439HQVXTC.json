{"__meta": {"id": "01K4F6RXJ16RQN90R439HQVXTC", "datetime": "2025-09-06 09:47:28", "utime": **********.706606, "method": "GET", "uri": "/admin/users", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.14716, "end": **********.706625, "duration": 0.5594649314880371, "duration_str": "559ms", "measures": [{"label": "Booting", "start": **********.14716, "relative_start": 0, "end": **********.34831, "relative_end": **********.34831, "duration": 0.*****************, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.348371, "relative_start": 0.*****************, "end": **********.706628, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.372277, "relative_start": 0.*****************, "end": **********.375464, "relative_end": **********.375464, "duration": 0.003186941146850586, "duration_str": "3.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.657613, "relative_start": 0.****************, "end": **********.703556, "relative_end": **********.703556, "duration": 0.*****************, "duration_str": "45.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Admin/UserManagement", "param_count": null, "params": [], "start": **********.706515, "type": "tsx", "hash": "tsxC:\\dev\\thesylink\\resources\\js/Pages/Admin/UserManagement.tsxAdmin/UserManagement", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fresources%2Fjs%2Fpages%2FAdmin%2FUserManagement.tsx&line=1", "ajax": false, "filename": "UserManagement.tsx", "line": "?"}}]}, "queries": {"count": 10, "nb_statements": 9, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.20987, "accumulated_duration_str": "210ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.392576, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy' limit 1", "type": "query", "params": [], "bindings": ["MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.4015708, "duration": 0.10769, "duration_str": "108ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 51.313}, {"sql": "select * from \"users\" where \"id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' limit 1", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.523376, "duration": 0.05112, "duration_str": "51.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 51.313, "width_percent": 24.358}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5897338, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "UserManagementController.php:52", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=52", "ajax": false, "filename": "UserManagementController.php", "line": "52"}, "connection": "thesylink", "explain": null, "start_percent": 75.671, "width_percent": 1.944}, {"sql": "select \"users\".*, (select count(*) from \"projects\" where \"users\".\"id\" = \"projects\".\"user_id\") as \"projects_count\", (select count(*) from \"bids\" where \"users\".\"id\" = \"bids\".\"user_id\") as \"bids_count\" from \"users\" order by \"created_at\" desc limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.595562, "duration": 0.02443, "duration_str": "24.43ms", "memory": 0, "memory_str": null, "filename": "UserManagementController.php:52", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=52", "ajax": false, "filename": "UserManagementController.php", "line": "52"}, "connection": "thesylink", "explain": null, "start_percent": 77.615, "width_percent": 11.641}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 59}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.62339, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "UserManagementController.php:59", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=59", "ajax": false, "filename": "UserManagementController.php", "line": "59"}, "connection": "thesylink", "explain": null, "start_percent": 89.255, "width_percent": 1.525}, {"sql": "select count(*) as aggregate from \"users\" where \"status\" = 'active'", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.6281629, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "UserManagementController.php:60", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=60", "ajax": false, "filename": "UserManagementController.php", "line": "60"}, "connection": "thesylink", "explain": null, "start_percent": 90.78, "width_percent": 1.444}, {"sql": "select count(*) as aggregate from \"users\" where \"status\" = 'suspended'", "type": "query", "params": [], "bindings": ["suspended"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.634423, "duration": 0.00771, "duration_str": "7.71ms", "memory": 0, "memory_str": null, "filename": "UserManagementController.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=61", "ajax": false, "filename": "UserManagementController.php", "line": "61"}, "connection": "thesylink", "explain": null, "start_percent": 92.224, "width_percent": 3.674}, {"sql": "select count(*) as aggregate from \"users\" where \"status\" = 'blocked'", "type": "query", "params": [], "bindings": ["blocked"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.643752, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "UserManagementController.php:62", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=62", "ajax": false, "filename": "UserManagementController.php", "line": "62"}, "connection": "thesylink", "explain": null, "start_percent": 95.897, "width_percent": 1.582}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 63}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.648814, "duration": 0.00529, "duration_str": "5.29ms", "memory": 0, "memory_str": null, "filename": "UserManagementController.php:63", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UserManagementController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\UserManagementController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=63", "ajax": false, "filename": "UserManagementController.php", "line": "63"}, "connection": "thesylink", "explain": null, "start_percent": 97.479, "width_percent": 2.521}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 11, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 11}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/users", "action_name": "admin.users.index", "controller_action": "App\\Http\\Controllers\\Admin\\UserManagementController@index", "uri": "GET admin/users", "controller": "App\\Http\\Controllers\\Admin\\UserManagementController@index<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserManagementController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/UserManagementController.php:17-70</a>", "middleware": "web, auth, verified, admin", "duration": "561ms", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-345240578 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-345240578\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1709863741 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1709863741\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-169025559 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ijd1b1hscHptK3F6VVVvT1kzQzRlTXc9PSIsInZhbHVlIjoiWURjVE1BQUt2a3JYNVI0cldpa013aVQ0OGlwMGVTYzlaWTJ2SUl1ZUlnYkVHTUhkRXRSbHphSVhrekRzRWRoc3ZwemR5OGVHRGdGUUhyWFhwOXN0MitVbllKNnBVZXZiVEtzaElHVmRMcVdPRmxMcVEyU0E2NGlnWHRsSVFWL3QiLCJtYWMiOiJlZTdhMzA5OWIzZTk0OGUxYTk5ZjM0NGM2NDg2YTJjN2I5NjJlYWMzZDEyYWYzMTQ1NDUwOGFmNzJlM2EyN2NiIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6Im9LaVVVZERVT29GNXVYQzIvdE9UeUE9PSIsInZhbHVlIjoieFp6MlFxcUxRaGkvV3JTMUpiQVdrT3BaV0VMc1o4YWExSG9rREw0bVl4RlI4YVVPNmtVbGNSbUVBRkV3amxSRjZ3MzNrZENVdUhraVpETkhHckpPVmVWUFh3R0pma3oveXpVRjB5dHZhdnlQNThONzRDU3NuRSs4T1lLbXhZZksiLCJtYWMiOiIyODZlYjBkOWQ2ZjhjYmVmZDU4YzBjZDRlOGU4MTQ0MjdhMmU1ZjI2YjgxMWFlYWMwYzFhOWMwM2QzMmQ1YjUwIiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6IjVmRnB6SGdLdUhZQjVvOUVZenhqS2c9PSIsInZhbHVlIjoiMys2a3Mxa0czQ3ArZ1Q4MkhiQW5OQUN5eEQrNm9xQ1NUNENQYVNuU1FOZHNqWHozMVZ4SXpJL3lmZldCT1cySDdGdGFObEQrQ3ZlSnozMEI5aEY2UHlUTGsyUWNPTlU2cXI4dkJKZ3Y3NEppOGRaR2N6VEdsSExvS2JuV05XU2UiLCJtYWMiOiIyNTA3YTU2YzEyYzI2M2U1MmQ2MDQwNmZlNzk5MWUzYTcyZjFjNTNhMmU3Mjc1MThlZjI3ZTA2OWFhNjU1MmY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169025559\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1603501793 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603501793\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1886991320 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 09:47:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1886991320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-213847801 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">01991e3e-2ac7-73a5-b86e-ba67f54988d5</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K4F6RWZV5EF1SAJX8QZEVDQ2</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">User unverified successfully.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213847801\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/users", "action_name": "admin.users.index", "controller_action": "App\\Http\\Controllers\\Admin\\UserManagementController@index"}, "badge": null}}