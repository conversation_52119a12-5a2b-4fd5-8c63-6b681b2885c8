{"__meta": {"id": "01K4F8KC152GXAXVA0S8PH5HMN", "datetime": "2025-09-06 10:19:24", "utime": **********.070981, "method": "GET", "uri": "/admin/analytics", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.37655, "end": **********.071001, "duration": 0.694451093673706, "duration_str": "694ms", "measures": [{"label": "Booting", "start": **********.37655, "relative_start": 0, "end": **********.668107, "relative_end": **********.668107, "duration": 0.*****************, "duration_str": "292ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.668127, "relative_start": 0.****************, "end": **********.071003, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.700982, "relative_start": 0.****************, "end": **********.705046, "relative_end": **********.705046, "duration": 0.004063844680786133, "duration_str": "4.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.043008, "relative_start": 0.****************, "end": **********.068023, "relative_end": **********.068023, "duration": 0.025014877319335938, "duration_str": "25.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Admin/Analytics", "param_count": null, "params": [], "start": **********.07088, "type": "tsx", "hash": "tsxC:\\dev\\thesylink\\resources\\js/Pages/Admin/Analytics.tsxAdmin/Analytics", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fresources%2Fjs%2Fpages%2FAdmin%2FAnalytics.tsx&line=1", "ajax": false, "filename": "Analytics.tsx", "line": "?"}}]}, "queries": {"count": 39, "nb_statements": 38, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.22392, "accumulated_duration_str": "224ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.729199, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy' limit 1", "type": "query", "params": [], "bindings": ["MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.736695, "duration": 0.08411, "duration_str": "84.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 37.563}, {"sql": "select * from \"users\" where \"id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' limit 1", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.833957, "duration": 0.009710000000000002, "duration_str": "9.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 37.563, "width_percent": 4.336}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 31}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.850851, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:31", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=31", "ajax": false, "filename": "AnalyticsController.php", "line": "31"}, "connection": "thesylink", "explain": null, "start_percent": 41.899, "width_percent": 1.643}, {"sql": "select count(*) as aggregate from \"users\" where extract(month from \"created_at\") = '09'", "type": "query", "params": [], "bindings": ["09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.856049, "duration": 0.00714, "duration_str": "7.14ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:32", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=32", "ajax": false, "filename": "AnalyticsController.php", "line": "32"}, "connection": "thesylink", "explain": null, "start_percent": 43.542, "width_percent": 3.189}, {"sql": "select count(*) as aggregate from \"users\" where \"status\" = 'active'", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.864621, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:33", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=33", "ajax": false, "filename": "AnalyticsController.php", "line": "33"}, "connection": "thesylink", "explain": null, "start_percent": 46.731, "width_percent": 1.992}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.870652, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:34", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=34", "ajax": false, "filename": "AnalyticsController.php", "line": "34"}, "connection": "thesylink", "explain": null, "start_percent": 48.723, "width_percent": 1.25}, {"sql": "select \"role\", count(*) as count from \"users\" group by \"role\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 37}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.876704, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:37", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=37", "ajax": false, "filename": "AnalyticsController.php", "line": "37"}, "connection": "thesylink", "explain": null, "start_percent": 49.973, "width_percent": 1.576}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.882112, "duration": 0.0058200000000000005, "duration_str": "5.82ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:43", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=43", "ajax": false, "filename": "AnalyticsController.php", "line": "43"}, "connection": "thesylink", "explain": null, "start_percent": 51.55, "width_percent": 2.599}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'open'", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.889518, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:44", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=44", "ajax": false, "filename": "AnalyticsController.php", "line": "44"}, "connection": "thesylink", "explain": null, "start_percent": 54.149, "width_percent": 1.067}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'in_progress'", "type": "query", "params": [], "bindings": ["in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.8930259, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=45", "ajax": false, "filename": "AnalyticsController.php", "line": "45"}, "connection": "thesylink", "explain": null, "start_percent": 55.216, "width_percent": 1.049}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.896874, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:46", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=46", "ajax": false, "filename": "AnalyticsController.php", "line": "46"}, "connection": "thesylink", "explain": null, "start_percent": 56.266, "width_percent": 1.108}, {"sql": "select \"category\", count(*) as count from \"projects\" where \"category\" is not null group by \"category\" order by \"count\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 52}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.9007652, "duration": 0.0062699999999999995, "duration_str": "6.27ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:52", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=52", "ajax": false, "filename": "AnalyticsController.php", "line": "52"}, "connection": "thesylink", "explain": null, "start_percent": 57.373, "width_percent": 2.8}, {"sql": "select \"academic_level\", count(*) as count from \"projects\" where \"academic_level\" is not null group by \"academic_level\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.908959, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:57", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=57", "ajax": false, "filename": "AnalyticsController.php", "line": "57"}, "connection": "thesylink", "explain": null, "start_percent": 60.173, "width_percent": 1.514}, {"sql": "select sum(\"wallet_balance\") as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 65}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.913623, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:65", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=65", "ajax": false, "filename": "AnalyticsController.php", "line": "65"}, "connection": "thesylink", "explain": null, "start_percent": 61.687, "width_percent": 1.929}, {"sql": "select count(*) as aggregate from \"wallet_transactions\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.919555, "duration": 0.00656, "duration_str": "6.56ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:66", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=66", "ajax": false, "filename": "AnalyticsController.php", "line": "66"}, "connection": "thesylink", "explain": null, "start_percent": 63.616, "width_percent": 2.93}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'deposit' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["deposit", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.927196, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:67", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=67", "ajax": false, "filename": "AnalyticsController.php", "line": "67"}, "connection": "thesylink", "explain": null, "start_percent": 66.546, "width_percent": 1.103}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["withdrawal", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.930816, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=68", "ajax": false, "filename": "AnalyticsController.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 67.649, "width_percent": 1.058}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["withdrawal", "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.934481, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:69", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=69", "ajax": false, "filename": "AnalyticsController.php", "line": "69"}, "connection": "thesylink", "explain": null, "start_percent": 68.708, "width_percent": 1.188}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END) as deposits, SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END) as withdrawals from \"wallet_transactions\" where extract(year from \"created_at\") = 2025 group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": [2025], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 78}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.938745, "duration": 0.00754, "duration_str": "7.54ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:78", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 78}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=78", "ajax": false, "filename": "AnalyticsController.php", "line": "78"}, "connection": "thesylink", "explain": null, "start_percent": 69.895, "width_percent": 3.367}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 85}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.951643, "duration": 0.00983, "duration_str": "9.83ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:85", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=85", "ajax": false, "filename": "AnalyticsController.php", "line": "85"}, "connection": "thesylink", "explain": null, "start_percent": 73.263, "width_percent": 4.39}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.96264, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:86", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=86", "ajax": false, "filename": "AnalyticsController.php", "line": "86"}, "connection": "thesylink", "explain": null, "start_percent": 77.653, "width_percent": 1.572}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 87}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.967459, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:87", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=87", "ajax": false, "filename": "AnalyticsController.php", "line": "87"}, "connection": "thesylink", "explain": null, "start_percent": 79.225, "width_percent": 1.313}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'rejected'", "type": "query", "params": [], "bindings": ["rejected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 88}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.971775, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:88", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=88", "ajax": false, "filename": "AnalyticsController.php", "line": "88"}, "connection": "thesylink", "explain": null, "start_percent": 80.538, "width_percent": 1.3}, {"sql": "select avg(\"amount\") as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.976033, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:93", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=93", "ajax": false, "filename": "AnalyticsController.php", "line": "93"}, "connection": "thesylink", "explain": null, "start_percent": 81.837, "width_percent": 1.313}, {"sql": "select \"id\", \"name\", \"email\", \"created_at\", \"role\" from \"users\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 98}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.980444, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:98", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=98", "ajax": false, "filename": "AnalyticsController.php", "line": "98"}, "connection": "thesylink", "explain": null, "start_percent": 83.15, "width_percent": 1.496}, {"sql": "select \"id\", \"title\", \"user_id\", \"status\", \"created_at\" from \"projects\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.985222, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:99", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=99", "ajax": false, "filename": "AnalyticsController.php", "line": "99"}, "connection": "thesylink", "explain": null, "start_percent": 84.646, "width_percent": 1.25}, {"sql": "select \"id\", \"name\" from \"users\" where \"users\".\"id\" in ('01991e3e-2cd2-7050-ae72-4e0beef93fb6', '01991e3e-2dde-736d-982b-0cac6a8d8141', '01991e3e-2f04-704f-b98a-ab1d99f5cc76')", "type": "query", "params": [], "bindings": ["01991e3e-2cd2-7050-ae72-4e0beef93fb6", "01991e3e-2dde-736d-982b-0cac6a8d8141", "01991e3e-2f04-704f-b98a-ab1d99f5cc76"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.9922361, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:99", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=99", "ajax": false, "filename": "AnalyticsController.php", "line": "99"}, "connection": "thesylink", "explain": null, "start_percent": 85.897, "width_percent": 1.304}, {"sql": "select \"id\", \"user_id\", \"type\", \"amount\", \"status\", \"created_at\" from \"wallet_transactions\" order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.996787, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:107", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=107", "ajax": false, "filename": "AnalyticsController.php", "line": "107"}, "connection": "thesylink", "explain": null, "start_percent": 87.201, "width_percent": 1.17}, {"sql": "select \"id\", \"name\" from \"users\" where \"users\".\"id\" in ('01991e3e-2ac7-73a5-b86e-ba67f54988d5')", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.00084, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:107", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=107", "ajax": false, "filename": "AnalyticsController.php", "line": "107"}, "connection": "thesylink", "explain": null, "start_percent": 88.371, "width_percent": 1.099}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"users\" where \"created_at\" >= '2024-09-06 10:19:24' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 10:19:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.004805, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:119", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=119", "ajax": false, "filename": "AnalyticsController.php", "line": "119"}, "connection": "thesylink", "explain": null, "start_percent": 89.469, "width_percent": 1.259}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"projects\" where \"created_at\" >= '2024-09-06 10:19:24' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 10:19:24"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 128}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.00917, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:128", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=128", "ajax": false, "filename": "AnalyticsController.php", "line": "128"}, "connection": "thesylink", "explain": null, "start_percent": 90.729, "width_percent": 1.175}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.013854, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:134", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=134", "ajax": false, "filename": "AnalyticsController.php", "line": "134"}, "connection": "thesylink", "explain": null, "start_percent": 91.903, "width_percent": 1.273}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.017776, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:134", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=134", "ajax": false, "filename": "AnalyticsController.php", "line": "134"}, "connection": "thesylink", "explain": null, "start_percent": 93.176, "width_percent": 1.072}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.0216398, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:135", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=135", "ajax": false, "filename": "AnalyticsController.php", "line": "135"}, "connection": "thesylink", "explain": null, "start_percent": 94.248, "width_percent": 1.054}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.025121, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:135", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=135", "ajax": false, "filename": "AnalyticsController.php", "line": "135"}, "connection": "thesylink", "explain": null, "start_percent": 95.302, "width_percent": 0.996}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.028794, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:136", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=136", "ajax": false, "filename": "AnalyticsController.php", "line": "136"}, "connection": "thesylink", "explain": null, "start_percent": 96.298, "width_percent": 1.112}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.032906, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:136", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=136", "ajax": false, "filename": "AnalyticsController.php", "line": "136"}, "connection": "thesylink", "explain": null, "start_percent": 97.41, "width_percent": 1.242}, {"sql": "select avg(\"accepted_bid_amount\") as aggregate from \"projects\" where \"accepted_bid_amount\" is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 141}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.0371969, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:141", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 141}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=141", "ajax": false, "filename": "AnalyticsController.php", "line": "141"}, "connection": "thesylink", "explain": null, "start_percent": 98.651, "width_percent": 1.349}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Project": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\WalletTransaction": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FWalletTransaction.php&line=1", "ajax": false, "filename": "WalletTransaction.php", "line": "?"}}}, "count": 21, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 21}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index", "uri": "GET admin/analytics", "controller": "App\\Http\\Controllers\\Admin\\AnalyticsController@index<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/AnalyticsController.php:20-160</a>", "middleware": "web, auth, verified, admin", "duration": "697ms", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2053139225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2053139225\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1833323596 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1833323596\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-716196046 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdxVzRQZlJxOERZZWRHcHlOZkU3TXc9PSIsInZhbHVlIjoidXVPaU5QaG54bHVObXNpcDl2WTlvckVLTkIvdnRsUURUQkl6ZjNuTWVLdGJvTVAyWG1salEybUlFdk5vVW5zaE9TdFZZNytNZW5pbUNSRmdNTUdrRDcweXYydW96VWs5NlNiNEhadDEvWjl6UjA5WEp0VW5ac1VzL3B2NW5ranMiLCJtYWMiOiI1ZjViYmIzMzc5NmY0ODM1NDJlOGM2MzE1ZjQwOTAzYjNmMDdlY2NmYjhjMmE3N2Y2OGNjMDhhNTIzYzNlMzg5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IjdxVzRQZlJxOERZZWRHcHlOZkU3TXc9PSIsInZhbHVlIjoidXVPaU5QaG54bHVObXNpcDl2WTlvckVLTkIvdnRsUURUQkl6ZjNuTWVLdGJvTVAyWG1salEybUlFdk5vVW5zaE9TdFZZNytNZW5pbUNSRmdNTUdrRDcweXYydW96VWs5NlNiNEhadDEvWjl6UjA5WEp0VW5ac1VzL3B2NW5ranMiLCJtYWMiOiI1ZjViYmIzMzc5NmY0ODM1NDJlOGM2MzE1ZjQwOTAzYjNmMDdlY2NmYjhjMmE3N2Y2OGNjMDhhNTIzYzNlMzg5IiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6ImR2SldXVjJ1aE4rTUNlczJkeWtKa1E9PSIsInZhbHVlIjoidG0wa0FGUXJWMlF6QTRENVR4Wng5aWg3TGlpa2NidWY1bllJa2d5UlNFT3RnQ3BCbVlNalpjSmw4SGxKcWRwNU9QTTZvSk5Dd2l3UnJ0NndORVQyYWNEOUtDeUtLSDlUelBiZURzQWh5Z0ZRWWpidmJEV2Nlc1BXR0ZnVmxpTkIiLCJtYWMiOiIyZGFlZTNmMjUyMGM1NTE2OTRiODFmNmE3YmY1NzgwNjM4Mzg2OGQ1NWZkYjdkZDdhZmQ3YTI2MjM5Y2NkMTk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-716196046\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1769010778 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769010778\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-567749292 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 10:19:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567749292\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-700212989 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost:8000/admin/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">01991e3e-2ac7-73a5-b86e-ba67f54988d5</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700212989\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index"}, "badge": null}}