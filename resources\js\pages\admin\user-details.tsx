import { <PERSON><PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { PageProps } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Briefcase, Calendar, Clock, Edit, Mail, MapPin, Shield, Star } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    role: string;
    status: 'active' | 'inactive' | 'suspended';
    bio: string | null;
    skills: string | null;
    location: string | null;
    created_at: string;
    updated_at: string;
    last_login: string | null;
    projects_count: number;
    bids_count: number;
    completed_projects_count: number;
    average_rating: number | null;
    total_earnings: number;
}

interface Project {
    id: number;
    title: string;
    status: string;
    budget: number;
    created_at: string;
}

interface UserDetailsProps extends PageProps {
    user: User;
    recentProjects: Project[];
}

export default function UserDetails({ auth, user, recentProjects }: UserDetailsProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800';
            case 'inactive':
                return 'bg-gray-100 text-gray-800';
            case 'suspended':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getRoleColor = (role: string) => {
        switch (role) {
            case 'admin':
                return 'bg-purple-100 text-purple-800';
            case 'moderator':
                return 'bg-blue-100 text-blue-800';
            case 'user':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AppLayout>
            <Head title={`User - ${user.name}`} />

            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Link href="/admin/users">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Users
                        </Button>
                    </Link>
                    <div className="flex items-center gap-2">
                        <Shield className="h-6 w-6" />
                        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">User Details</h1>
                    </div>
                </div>

                <div className="py-12">
                    <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                            {/* User Profile Card */}
                            <div className="lg:col-span-1">
                                <Card>
                                    <CardContent className="p-6">
                                        <div className="text-center">
                                            <Avatar className="mx-auto mb-4 h-24 w-24">
                                                <AvatarFallback className="text-2xl">{user.name.charAt(0).toUpperCase()}</AvatarFallback>
                                            </Avatar>
                                            <h3 className="text-xl font-semibold">{user.name}</h3>
                                            <p className="mb-4 text-gray-600 dark:text-gray-400">{user.email}</p>

                                            <div className="mb-4 flex justify-center gap-2">
                                                <Badge className={getRoleColor(user.role)}>{user.role}</Badge>
                                                <Badge className={getStatusColor(user.status)}>{user.status}</Badge>
                                                {!user.email_verified_at && <Badge variant="outline">Unverified</Badge>}
                                            </div>

                                            <Link href={`/admin/users/${user.id}/edit`}>
                                                <Button className="w-full">
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit User
                                                </Button>
                                            </Link>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Quick Stats */}
                                <Card className="mt-6">
                                    <CardHeader>
                                        <CardTitle>Quick Stats</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">Projects Posted</span>
                                            <span className="font-medium">{user.projects_count}</span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">Bids Submitted</span>
                                            <span className="font-medium">{user.bids_count}</span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">Completed Projects</span>
                                            <span className="font-medium">{user.completed_projects_count}</span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">Total Earnings</span>
                                            <span className="font-medium">{formatCurrency(user.total_earnings)}</span>
                                        </div>
                                        {user.average_rating && (
                                            <div className="flex items-center justify-between">
                                                <span className="text-gray-600 dark:text-gray-400">Average Rating</span>
                                                <div className="flex items-center gap-1">
                                                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                                    <span className="font-medium">{user.average_rating.toFixed(1)}</span>
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Main Content */}
                            <div className="space-y-6 lg:col-span-2">
                                {/* User Information */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>User Information</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div className="flex items-center gap-3">
                                                <Mail className="h-5 w-5 text-gray-400" />
                                                <div>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">Email</p>
                                                    <p className="font-medium">{user.email}</p>
                                                </div>
                                            </div>

                                            <div className="flex items-center gap-3">
                                                <Calendar className="h-5 w-5 text-gray-400" />
                                                <div>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">Joined</p>
                                                    <p className="font-medium">{formatDate(user.created_at)}</p>
                                                </div>
                                            </div>

                                            {user.location && (
                                                <div className="flex items-center gap-3">
                                                    <MapPin className="h-5 w-5 text-gray-400" />
                                                    <div>
                                                        <p className="text-sm text-gray-600 dark:text-gray-400">Location</p>
                                                        <p className="font-medium">{user.location}</p>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="flex items-center gap-3">
                                                <Clock className="h-5 w-5 text-gray-400" />
                                                <div>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">Last Login</p>
                                                    <p className="font-medium">{user.last_login ? formatDate(user.last_login) : 'Never'}</p>
                                                </div>
                                            </div>
                                        </div>

                                        {user.bio && (
                                            <>
                                                <Separator />
                                                <div>
                                                    <h4 className="mb-2 font-medium">Biography</h4>
                                                    <p className="text-gray-600 dark:text-gray-400">{user.bio}</p>
                                                </div>
                                            </>
                                        )}

                                        {user.skills && (
                                            <>
                                                <Separator />
                                                <div>
                                                    <h4 className="mb-2 font-medium">Skills</h4>
                                                    <div className="flex flex-wrap gap-2">
                                                        {user.skills.split(',').map((skill, index) => (
                                                            <Badge key={index} variant="secondary">
                                                                {skill.trim()}
                                                            </Badge>
                                                        ))}
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Recent Projects */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Briefcase className="h-5 w-5" />
                                            Recent Projects
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {recentProjects.length > 0 ? (
                                            <div className="space-y-4">
                                                {recentProjects.map((project) => (
                                                    <div key={project.id} className="rounded-lg border p-4">
                                                        <div className="flex items-start justify-between">
                                                            <div>
                                                                <h4 className="font-medium">{project.title}</h4>
                                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                                    Budget: {formatCurrency(project.budget)}
                                                                </p>
                                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                                    Created: {formatDate(project.created_at)}
                                                                </p>
                                                            </div>
                                                            <Badge variant="outline">{project.status}</Badge>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="py-8 text-center text-gray-600 dark:text-gray-400">No recent projects found.</p>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
