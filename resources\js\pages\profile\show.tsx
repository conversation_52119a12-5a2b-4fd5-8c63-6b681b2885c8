import { <PERSON><PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { PageProps } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { Briefcase, Calendar, Edit, Mail, MapPin, Save, Star, User, X } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    bio: string | null;
    skills: string | null;
    location: string | null;
    phone: string | null;
    website: string | null;
    created_at: string;
    projects_count: number;
    completed_projects_count: number;
    average_rating: number | null;
    total_earnings: number;
}

interface ProfileShowProps extends PageProps {
    user: User;
    profileUser: User; // The user whose profile we're viewing
    isOwnProfile: boolean;
}

export default function ProfileShow({ auth, user, profileUser, isOwnProfile }: ProfileShowProps) {
    const [isEditing, setIsEditing] = useState(false);

    const { data, setData, patch, processing, errors, reset } = useForm({
        name: profileUser.name || '',
        bio: profileUser.bio || '',
        skills: profileUser.skills || '',
        location: profileUser.location || '',
        phone: profileUser.phone || '',
        website: profileUser.website || '',
    });

    const handleSave = (e: React.FormEvent) => {
        e.preventDefault();
        patch(`/profile/${profileUser.id}`, {
            onSuccess: () => {
                setIsEditing(false);
            },
        });
    };

    const handleCancel = () => {
        reset();
        setIsEditing(false);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AppLayout>
            <Head title={isOwnProfile ? 'My Profile' : `${profileUser.name}'s Profile`} />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <User className="h-6 w-6" />
                        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                            {isOwnProfile ? 'My Profile' : `${profileUser.name}'s Profile`}
                        </h1>
                    </div>
                    {isOwnProfile && !isEditing && (
                        <Button onClick={() => setIsEditing(true)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Profile
                        </Button>
                    )}
                </div>

                <div className="py-12">
                    <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                            {/* Profile Summary */}
                            <div className="lg:col-span-1">
                                <Card>
                                    <CardContent className="p-6">
                                        <div className="text-center">
                                            <Avatar className="mx-auto mb-4 h-24 w-24">
                                                <AvatarFallback className="text-2xl">{profileUser.name.charAt(0).toUpperCase()}</AvatarFallback>
                                            </Avatar>
                                            <h3 className="text-xl font-semibold">{profileUser.name}</h3>
                                            <p className="mb-2 text-gray-600 dark:text-gray-400">{profileUser.email}</p>

                                            {!profileUser.email_verified_at && (
                                                <Badge variant="outline" className="mb-4">
                                                    Email not verified
                                                </Badge>
                                            )}

                                            {profileUser.location && (
                                                <div className="mb-4 flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
                                                    <MapPin className="h-4 w-4" />
                                                    <span>{profileUser.location}</span>
                                                </div>
                                            )}

                                            <div className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
                                                <Calendar className="h-4 w-4" />
                                                <span>Joined {formatDate(profileUser.created_at)}</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Stats Card */}
                                <Card className="mt-6">
                                    <CardHeader>
                                        <CardTitle>Statistics</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">Projects Posted</span>
                                            <span className="font-medium">{profileUser.projects_count}</span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">Completed Projects</span>
                                            <span className="font-medium">{profileUser.completed_projects_count}</span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">Total Earnings</span>
                                            <span className="font-medium">{formatCurrency(profileUser.total_earnings)}</span>
                                        </div>
                                        {profileUser.average_rating && (
                                            <div className="flex items-center justify-between">
                                                <span className="text-gray-600 dark:text-gray-400">Average Rating</span>
                                                <div className="flex items-center gap-1">
                                                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                                    <span className="font-medium">{profileUser.average_rating.toFixed(1)}</span>
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Main Content */}
                            <div className="space-y-6 lg:col-span-2">
                                {/* Profile Information */}
                                <Card>
                                    <CardHeader>
                                        <div className="flex items-center justify-between">
                                            <CardTitle>Profile Information</CardTitle>
                                            {isEditing && (
                                                <div className="flex gap-2">
                                                    <Button onClick={handleSave} disabled={processing} size="sm">
                                                        <Save className="mr-2 h-4 w-4" />
                                                        Save
                                                    </Button>
                                                    <Button onClick={handleCancel} variant="outline" size="sm">
                                                        <X className="mr-2 h-4 w-4" />
                                                        Cancel
                                                    </Button>
                                                </div>
                                            )}
                                        </div>
                                    </CardHeader>
                                    <CardContent>
                                        {isEditing ? (
                                            <form onSubmit={handleSave} className="space-y-6">
                                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="name">Full Name</Label>
                                                        <Input
                                                            id="name"
                                                            value={data.name}
                                                            onChange={(e) => setData('name', e.target.value)}
                                                            placeholder="Enter your full name"
                                                        />
                                                        {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="location">Location</Label>
                                                        <Input
                                                            id="location"
                                                            value={data.location}
                                                            onChange={(e) => setData('location', e.target.value)}
                                                            placeholder="Enter your location"
                                                        />
                                                        {errors.location && <p className="text-sm text-red-600">{errors.location}</p>}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="phone">Phone</Label>
                                                        <Input
                                                            id="phone"
                                                            value={data.phone}
                                                            onChange={(e) => setData('phone', e.target.value)}
                                                            placeholder="Enter your phone number"
                                                        />
                                                        {errors.phone && <p className="text-sm text-red-600">{errors.phone}</p>}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor="website">Website</Label>
                                                        <Input
                                                            id="website"
                                                            value={data.website}
                                                            onChange={(e) => setData('website', e.target.value)}
                                                            placeholder="Enter your website URL"
                                                        />
                                                        {errors.website && <p className="text-sm text-red-600">{errors.website}</p>}
                                                    </div>
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="bio">Biography</Label>
                                                    <Textarea
                                                        id="bio"
                                                        value={data.bio}
                                                        onChange={(e) => setData('bio', e.target.value)}
                                                        placeholder="Tell us about yourself..."
                                                        rows={4}
                                                    />
                                                    {errors.bio && <p className="text-sm text-red-600">{errors.bio}</p>}
                                                </div>

                                                <div className="space-y-2">
                                                    <Label htmlFor="skills">Skills</Label>
                                                    <Input
                                                        id="skills"
                                                        value={data.skills}
                                                        onChange={(e) => setData('skills', e.target.value)}
                                                        placeholder="Enter skills separated by commas"
                                                    />
                                                    <p className="text-sm text-gray-600">
                                                        Separate multiple skills with commas (e.g., JavaScript, React, Node.js)
                                                    </p>
                                                    {errors.skills && <p className="text-sm text-red-600">{errors.skills}</p>}
                                                </div>
                                            </form>
                                        ) : (
                                            <div className="space-y-6">
                                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                    <div className="flex items-center gap-3">
                                                        <Mail className="h-5 w-5 text-gray-400" />
                                                        <div>
                                                            <p className="text-sm text-gray-600 dark:text-gray-400">Email</p>
                                                            <p className="font-medium">{profileUser.email}</p>
                                                        </div>
                                                    </div>

                                                    {profileUser.phone && (
                                                        <div className="flex items-center gap-3">
                                                            <span className="h-5 w-5 text-gray-400">📞</span>
                                                            <div>
                                                                <p className="text-sm text-gray-600 dark:text-gray-400">Phone</p>
                                                                <p className="font-medium">{profileUser.phone}</p>
                                                            </div>
                                                        </div>
                                                    )}

                                                    {profileUser.website && (
                                                        <div className="flex items-center gap-3">
                                                            <span className="h-5 w-5 text-gray-400">🌐</span>
                                                            <div>
                                                                <p className="text-sm text-gray-600 dark:text-gray-400">Website</p>
                                                                <a
                                                                    href={profileUser.website}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="font-medium text-blue-600 hover:underline"
                                                                >
                                                                    {profileUser.website}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>

                                                {profileUser.bio && (
                                                    <>
                                                        <Separator />
                                                        <div>
                                                            <h4 className="mb-2 font-medium">About Me</h4>
                                                            <p className="text-gray-600 dark:text-gray-400">{profileUser.bio}</p>
                                                        </div>
                                                    </>
                                                )}

                                                {profileUser.skills && (
                                                    <>
                                                        <Separator />
                                                        <div>
                                                            <h4 className="mb-2 font-medium">Skills</h4>
                                                            <div className="flex flex-wrap gap-2">
                                                                {profileUser.skills.split(',').map((skill, index) => (
                                                                    <Badge key={index} variant="secondary">
                                                                        {skill.trim()}
                                                                    </Badge>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Portfolio/Projects could go here */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Briefcase className="h-5 w-5" />
                                            Recent Work
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="py-8 text-center text-gray-600 dark:text-gray-400">Portfolio section coming soon...</p>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
