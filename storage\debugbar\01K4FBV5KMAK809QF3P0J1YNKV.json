{"__meta": {"id": "01K4FBV5KMAK809QF3P0J1YNKV", "datetime": "2025-09-06 11:16:05", "utime": **********.364643, "method": "GET", "uri": "/notifications/dropdown", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.072783, "end": **********.364674, "duration": 0.29189109802246094, "duration_str": "292ms", "measures": [{"label": "Booting", "start": **********.072783, "relative_start": 0, "end": **********.202491, "relative_end": **********.202491, "duration": 0.*****************, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.202502, "relative_start": 0.*****************, "end": **********.364675, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.21892, "relative_start": 0.*****************, "end": **********.22135, "relative_end": **********.22135, "duration": 0.002429962158203125, "duration_str": "2.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.362507, "relative_start": 0.*****************, "end": **********.362878, "relative_end": **********.362878, "duration": 0.00037097930908203125, "duration_str": "371μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09611999999999998, "accumulated_duration_str": "96.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.232628, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'BsOOWwA9W74Nk0Eg2P403i6MRB7jw5qaSeR6GHnZ' limit 1", "type": "query", "params": [], "bindings": ["BsOOWwA9W74Nk0Eg2P403i6MRB7jw5qaSeR6GHnZ"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.236968, "duration": 0.07912999999999999, "duration_str": "79.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 82.324}, {"sql": "select * from \"users\" where \"id\" = '01991e3e-2bc7-708c-93fd-218f2776adf0' limit 1", "type": "query", "params": [], "bindings": ["01991e3e-2bc7-708c-93fd-218f2776adf0"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.329458, "duration": 0.007980000000000001, "duration_str": "7.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 82.324, "width_percent": 8.302}, {"sql": "select * from \"notifications\" where \"notifications\".\"notifiable_type\" = 'App\\Models\\User' and \"notifications\".\"notifiable_id\" = '01991e3e-2bc7-708c-93fd-218f2776adf0' and \"notifications\".\"notifiable_id\" is not null order by \"created_at\" desc, \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": ["App\\Models\\User", "01991e3e-2bc7-708c-93fd-218f2776adf0"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/NotificationController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\NotificationController.php", "line": 79}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.346515, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:79", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/NotificationController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\NotificationController.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FNotificationController.php&line=79", "ajax": false, "filename": "NotificationController.php", "line": "79"}, "connection": "thesylink", "explain": null, "start_percent": 90.626, "width_percent": 6.201}, {"sql": "select count(*) as aggregate from \"notifications\" where \"notifications\".\"notifiable_type\" = 'App\\Models\\User' and \"notifications\".\"notifiable_id\" = '01991e3e-2bc7-708c-93fd-218f2776adf0' and \"notifications\".\"notifiable_id\" is not null and \"read_at\" is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", "01991e3e-2bc7-708c-93fd-218f2776adf0"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/NotificationController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\NotificationController.php", "line": 98}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.356464, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "NotificationController.php:98", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/NotificationController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\NotificationController.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FNotificationController.php&line=98", "ajax": false, "filename": "NotificationController.php", "line": "98"}, "connection": "thesylink", "explain": null, "start_percent": 96.827, "width_percent": 3.173}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/notifications/dropdown", "action_name": "notifications.dropdown", "controller_action": "App\\Http\\Controllers\\NotificationController@dropdown", "uri": "GET notifications/dropdown", "controller": "App\\Http\\Controllers\\NotificationController@dropdown<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FNotificationController.php&line=62\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/notifications", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FNotificationController.php&line=62\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/NotificationController.php:62-115</a>", "middleware": "web, auth, verified", "duration": "292ms", "peak_memory": "24MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-318018341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-318018341\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1822046534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1822046534\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-950935136 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">ZAIGvyycXz7rL57DiaeiqhFi8LaRaHr1GKIMaozb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjAwMTUvanR2dU9sN0NpRHFyV3lGN2c9PSIsInZhbHVlIjoib3pLUmtVQkdvN1FoanRXV0R5TXlQL3BjbDR4NmhIQ0tZcXNlZkxKUkdhUDRJVExZZHh6SmtBUDlvMEJWVzBmNmtQSUF0OEhBelltWW1VNVlsVnovL09wY25qMTJTejlYeVA3RndJcis4WHQrUmp5S082QWpJNW5wYitBL0I0SzMiLCJtYWMiOiJkZGI5MjdiMGZkMzNhOGJjYzcyOTlkYWMzZjE4YzRlYTIxMzdmN2ZmYzZmZmFlNzE0YmMzOGEzOWI5NGU3ZmM1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IjAwMTUvanR2dU9sN0NpRHFyV3lGN2c9PSIsInZhbHVlIjoib3pLUmtVQkdvN1FoanRXV0R5TXlQL3BjbDR4NmhIQ0tZcXNlZkxKUkdhUDRJVExZZHh6SmtBUDlvMEJWVzBmNmtQSUF0OEhBelltWW1VNVlsVnovL09wY25qMTJTejlYeVA3RndJcis4WHQrUmp5S082QWpJNW5wYitBL0I0SzMiLCJtYWMiOiJkZGI5MjdiMGZkMzNhOGJjYzcyOTlkYWMzZjE4YzRlYTIxMzdmN2ZmYzZmZmFlNzE0YmMzOGEzOWI5NGU3ZmM1IiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6IisvdlZHQkRvSHdFZW01dGhJaGk2R3c9PSIsInZhbHVlIjoic1VUZDJQb0x1dkFleUtUeGR4WmR1RjJGWi84SnBaeGtuYWQwZm0vc3JBRHMvMkdOQnJGQk5Sb1RFWXlTbWRJSXo2aVQwc0ZVVXVnKzNuWWg0MEp6MDlBRlN4UnEvY0Y5aWRBRXZOTTdUSGhaaEdncTR5bTZScS9md3I2M3ZEMy8iLCJtYWMiOiJlZWNjMzlmMTczYmE5MjM2YzRmMTIwNjAyMzM0MmYwODJiYTIyZjBlNDRmNDFlY2Y3ZmM1ZTgyZDY2N2RjYzZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950935136\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-483109410 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZAIGvyycXz7rL57DiaeiqhFi8LaRaHr1GKIMaozb</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BsOOWwA9W74Nk0Eg2P403i6MRB7jw5qaSeR6GHnZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483109410\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1279032045 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 11:16:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279032045\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-854715363 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZAIGvyycXz7rL57DiaeiqhFi8LaRaHr1GKIMaozb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/browse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">01991e3e-2bc7-708c-93fd-218f2776adf0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854715363\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/notifications/dropdown", "action_name": "notifications.dropdown", "controller_action": "App\\Http\\Controllers\\NotificationController@dropdown"}, "badge": null}}