<?php

namespace App\Models;

use App\Services\EscrowService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Bid extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'amount',
        'proposal',
        'delivery_days',
        'status',
        'commission_amount',
        'freelancer_amount',
        'commission_rate',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'freelancer_amount' => 'decimal:2',
        'commission_rate' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($bid) {
            $bid->calculateAndSetCommissionFields();
        });

        static::updating(function ($bid) {
            if ($bid->isDirty('amount')) {
                $bid->calculateAndSetCommissionFields();
            }
        });
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the commission amount for this bid
     */
    public function getCommissionAmountAttribute(): float
    {
        // Use stored value if available, otherwise calculate
        if (isset($this->attributes['commission_amount']) && $this->attributes['commission_amount'] !== null) {
            return (float) $this->attributes['commission_amount'];
        }

        return round($this->amount * (EscrowService::COMMISSION_RATE / 100), 2);
    }

    /**
     * Get the amount the freelancer will receive after commission
     */
    public function getFreelancerAmountAttribute(): float
    {
        // Use stored value if available, otherwise calculate
        if (isset($this->attributes['freelancer_amount']) && $this->attributes['freelancer_amount'] !== null) {
            return (float) $this->attributes['freelancer_amount'];
        }

        return round($this->amount - $this->commission_amount, 2);
    }

    /**
     * Get the commission rate
     */
    public function getCommissionRateAttribute(): float
    {
        // Use stored value if available, otherwise use current rate
        if (isset($this->attributes['commission_rate']) && $this->attributes['commission_rate'] !== null) {
            return (float) $this->attributes['commission_rate'];
        }

        return EscrowService::COMMISSION_RATE;
    }

    /**
     * Get commission breakdown for this bid
     */
    public function getCommissionBreakdown(): array
    {
        return [
            'gross_amount' => (float) $this->amount,
            'commission_amount' => $this->commission_amount,
            'net_amount' => $this->freelancer_amount,
            'commission_rate' => $this->commission_rate,
        ];
    }

    /**
     * Calculate commission breakdown for a given amount
     */
    public static function calculateCommissionBreakdown(float $amount): array
    {
        $commissionAmount = round($amount * (EscrowService::COMMISSION_RATE / 100), 2);
        $freelancerAmount = round($amount - $commissionAmount, 2);

        return [
            'gross_amount' => $amount,
            'commission_amount' => $commissionAmount,
            'net_amount' => $freelancerAmount,
            'commission_rate' => EscrowService::COMMISSION_RATE,
        ];
    }

    /**
     * Calculate and set commission fields for this bid
     */
    protected function calculateAndSetCommissionFields(): void
    {
        if ($this->amount) {
            $this->commission_rate = EscrowService::COMMISSION_RATE;
            $this->commission_amount = round($this->amount * (EscrowService::COMMISSION_RATE / 100), 2);
            $this->freelancer_amount = round($this->amount - $this->commission_amount, 2);
        }
    }
}
