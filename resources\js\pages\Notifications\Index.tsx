import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Bell, CheckCircle, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Notification {
    id: string;
    type: string;
    data: {
        type: string;
        title: string;
        message: string;
        action_url?: string;
        project_title?: string;
        freelancer_name?: string;
        client_name?: string;
    };
    read_at: string | null;
    created_at: string;
}

interface Props {
    notifications: {
        data: Notification[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    unread_count: number;
}

export default function NotificationsIndex({ notifications, unread_count }: Props) {
    const [loading, setLoading] = useState(false);
    const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Notifications',
            href: '/notifications',
        },
    ];

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case 'bid_submitted':
                return '📝';
            case 'bid_accepted':
                return '✅';
            case 'bid_rejected':
                return '❌';
            case 'milestone_completed':
                return '🎯';
            case 'payment_released':
                return '💰';
            default:
                return '📢';
        }
    };

    const getNotificationColor = (type: string) => {
        switch (type) {
            case 'bid_accepted':
            case 'payment_released':
                return 'text-green-600 bg-green-50 border-green-200';
            case 'bid_rejected':
                return 'text-red-600 bg-red-50 border-red-200';
            case 'bid_submitted':
            case 'milestone_completed':
                return 'text-blue-600 bg-blue-50 border-blue-200';
            default:
                return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const markAsRead = async (notificationId: string) => {
        setLoading(true);
        try {
            await router.patch(
                `/notifications/${notificationId}/read`,
                {},
                {
                    preserveState: true,
                    preserveScroll: true,
                    onSuccess: () => {
                        // Refresh to show updated state
                        router.reload();
                    },
                },
            );
        } finally {
            setLoading(false);
        }
    };

    const markAllAsRead = async () => {
        setLoading(true);
        try {
            await router.post(
                '/notifications/mark-all-read',
                {},
                {
                    preserveState: true,
                    preserveScroll: true,
                    onSuccess: () => {
                        router.reload();
                    },
                },
            );
        } finally {
            setLoading(false);
        }
    };

    const deleteNotification = async (notificationId: string) => {
        setDeletingIds((prev) => new Set(prev).add(notificationId));
        try {
            await router.delete(`/notifications/${notificationId}`, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    router.reload();
                },
            });
        } finally {
            setDeletingIds((prev) => {
                const newSet = new Set(prev);
                newSet.delete(notificationId);
                return newSet;
            });
        }
    };

    const handleNotificationClick = (notification: Notification) => {
        // Mark as read if unread
        if (!notification.read_at) {
            markAsRead(notification.id);
        }

        // Navigate to action URL if available
        if (notification.data.action_url && notification.data.action_url !== '#') {
            router.visit(notification.data.action_url);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Notifications" />

            <div className="mx-auto max-w-4xl space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Notifications</h1>
                        <p className="text-muted-foreground">
                            {notifications.total > 0 ? `${notifications.total} total notifications, ${unread_count} unread` : 'No notifications yet'}
                        </p>
                    </div>
                    {unread_count > 0 && (
                        <Button onClick={markAllAsRead} disabled={loading}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Mark All Read
                        </Button>
                    )}
                </div>

                {/* Notifications List */}
                {notifications.data.length > 0 ? (
                    <div className="space-y-4">
                        {notifications.data.map((notification) => (
                            <Card
                                key={notification.id}
                                className={cn(
                                    'cursor-pointer transition-all hover:shadow-md',
                                    !notification.read_at && 'border-l-4 border-l-primary bg-primary/5',
                                )}
                            >
                                <CardContent className="p-4">
                                    <div className="flex items-start justify-between gap-4">
                                        <div className="flex flex-1 items-start gap-3" onClick={() => handleNotificationClick(notification)}>
                                            <div
                                                className={cn(
                                                    'flex h-10 w-10 items-center justify-center rounded-full text-lg',
                                                    getNotificationColor(notification.data.type),
                                                )}
                                            >
                                                {getNotificationIcon(notification.data.type)}
                                            </div>
                                            <div className="min-w-0 flex-1">
                                                <div className="mb-1 flex items-center gap-2">
                                                    <h3 className="font-medium text-foreground">{notification.data.title}</h3>
                                                    {!notification.read_at && (
                                                        <Badge variant="default" className="h-5 text-xs">
                                                            New
                                                        </Badge>
                                                    )}
                                                </div>
                                                <p className="mb-2 text-sm text-muted-foreground">{notification.data.message}</p>
                                                <p className="text-xs text-muted-foreground">{formatDate(notification.created_at)}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {!notification.read_at && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        markAsRead(notification.id);
                                                    }}
                                                    disabled={loading}
                                                >
                                                    <CheckCircle className="h-4 w-4" />
                                                </Button>
                                            )}
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    deleteNotification(notification.id);
                                                }}
                                                disabled={deletingIds.has(notification.id)}
                                                className="text-destructive hover:text-destructive"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Bell className="mb-4 h-12 w-12 text-muted-foreground/50" />
                            <h3 className="mb-2 text-lg font-medium">No notifications</h3>
                            <p className="mb-4 text-center text-muted-foreground">
                                You don't have any notifications as of this moment.
                                <br />
                                When you receive proposals or project updates, they'll appear here.
                            </p>
                            <Button asChild variant="outline">
                                <Link href="/browse">Browse Projects</Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}

                {/* Pagination */}
                {notifications.last_page > 1 && (
                    <div className="flex items-center justify-center gap-2">
                        {notifications.current_page > 1 && (
                            <Button variant="outline" asChild>
                                <Link href={`/notifications?page=${notifications.current_page - 1}`}>Previous</Link>
                            </Button>
                        )}

                        <span className="text-sm text-muted-foreground">
                            Page {notifications.current_page} of {notifications.last_page}
                        </span>

                        {notifications.current_page < notifications.last_page && (
                            <Button variant="outline" asChild>
                                <Link href={`/notifications?page=${notifications.current_page + 1}`}>Next</Link>
                            </Button>
                        )}
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
