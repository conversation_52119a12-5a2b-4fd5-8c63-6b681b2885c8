import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign } from 'lucide-react';

interface TransactionHistoryProps {
    wallet_transactions: any[];
}

export default function TransactionHistory({ wallet_transactions }: TransactionHistoryProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5" />
                    <span>Recent Transactions</span>
                </CardTitle>
            </CardHeader>
            <CardContent>
                {wallet_transactions && wallet_transactions.length > 0 ? (
                    <div className="space-y-3">
                        {wallet_transactions.slice(0, 10).map((transaction: any, index: number) => (
                            <div key={transaction.id || index} className="flex items-center justify-between border-b pb-2 last:border-b-0">
                                <div>
                                    <p className="text-sm font-medium">{transaction.description}</p>
                                    <p className="text-xs text-muted-foreground">{new Date(transaction.created_at).toLocaleDateString()}</p>
                                </div>
                                <div className="text-right">
                                    <p className={`text-sm font-semibold ${transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'}`}>
                                        {transaction.type === 'credit' ? '+' : '-'}₵{transaction.amount}
                                    </p>
                                    <p className="text-xs text-muted-foreground capitalize">{transaction.status}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <p className="text-center text-muted-foreground">No transactions found</p>
                )}
            </CardContent>
        </Card>
    );
}
