{"__meta": {"id": "01K4F9STFVM2W56B26SJNE9JRH", "datetime": "2025-09-06 10:40:24", "utime": **********.060166, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[10:40:24] LOG.error: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd 875 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\n    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd:875:13)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:4206:24)\n    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:6461:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:7864:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10728:43)\n    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10711:13)\n    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10359:46) {\n    \"url\": \"http:\\/\\/localhost:8000\\/browse\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-09-06T10:40:22.841Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.033935, "xdebug_link": null, "collector": "log"}, {"message": "[10:40:24] LOG.error: Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd 875 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\n    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd:875:13)\n    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:17424:20)\n    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:4206:24)\n    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:6461:21)\n    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:7864:20)\n    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:1485:72)\n    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10868:98)\n    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10728:43)\n    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10711:13)\n    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10359:46) {\n    \"url\": \"http:\\/\\/localhost:8000\\/browse\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-09-06T10:40:22.842Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.034186, "xdebug_link": null, "collector": "log"}, {"message": "[10:40:24] LOG.warning: %s\n\n%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.\nVisit https://react.dev/link/error-boundaries to learn more about error boundaries. {\n    \"url\": \"http:\\/\\/localhost:8000\\/browse\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-09-06T10:40:22.843Z\"\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.039742, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.781816, "end": **********.060188, "duration": 0.27837204933166504, "duration_str": "278ms", "measures": [{"label": "Booting", "start": **********.781816, "relative_start": 0, "end": **********.997519, "relative_end": **********.997519, "duration": 0.*****************, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.997536, "relative_start": 0.*****************, "end": **********.06019, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "62.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.024333, "relative_start": 0.*****************, "end": **********.02729, "relative_end": **********.02729, "duration": 0.0029571056365966797, "duration_str": "2.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.057254, "relative_start": 0.****************, "end": **********.057719, "relative_end": **********.057719, "duration": 0.0004649162292480469, "duration_str": "465μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.057747, "relative_start": 0.*****************, "end": **********.057772, "relative_end": **********.057772, "duration": 2.5033950805664062e-05, "duration_str": "25μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1154}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 972}], "start": **********.052478, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=101\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:101-127</a>", "duration": "279ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-692308191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-692308191\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-229557800 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">uncaught_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-09-06T10:40:22.841Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"197 characters\">Uncaught Error: A &lt;Select.Item /&gt; must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"78 characters\">http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>875</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>13</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"181 characters\">A &lt;Select.Item /&gt; must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1266 characters\">Error: A &lt;Select.Item /&gt; must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd:875:13)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:6461:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:7864:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10728:43)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10711:13)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10359:46)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/browse</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">window_error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-09-06T10:40:22.842Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"197 characters\">Uncaught Error: A &lt;Select.Item /&gt; must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.</span>\"\n          \"<span class=sf-dump-key>filename</span>\" => \"<span class=sf-dump-str title=\"78 characters\">http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd</span>\"\n          \"<span class=sf-dump-key>lineno</span>\" => <span class=sf-dump-num>875</span>\n          \"<span class=sf-dump-key>colno</span>\" => <span class=sf-dump-num>13</span>\n          \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n            \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"181 characters\">A &lt;Select.Item /&gt; must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.</span>\"\n            \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1266 characters\">Error: A &lt;Select.Item /&gt; must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at SelectItem (http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=72859ecd:875:13)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:17424:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:4206:24)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at updateForwardRef (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:6461:21)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:7864:20)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:1485:72)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10868:98)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10728:43)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10711:13)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n              <span class=sf-dump-str title=\"1266 characters\">    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=72859ecd:10359:46)</span>\n              \"\"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/browse</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">warn</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-09-06T10:40:22.843Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"\"\"\n          <span class=sf-dump-str title=\"6 characters\">%s<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"6 characters\">%s</span>\n          \"\"\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"48 characters\">An error occurred in the &lt;SelectItem&gt; component.</span>\"\n        <span class=sf-dump-index>2</span> => \"\"\"\n          <span class=sf-dump-str title=\"168 characters\">Consider adding an error boundary to your tree to customize error handling behavior.<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n          <span class=sf-dump-str title=\"168 characters\">Visit https://react.dev/link/error-boundaries to learn more about error boundaries.</span>\n          \"\"\"\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/browse</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229557800\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-863533150 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4613</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/browse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6InlDZjZ6OWxNSnFWcmdKZUJxRFNmQ2c9PSIsInZhbHVlIjoiYjhpVVFSbGJKSzBadzY4YVpoY0VPNUREVXNsN1A1VGZBWk9tRDcrZEtBUlFxdHZyQk1FYXVaQ05RMzYyVGVWZkpKczExNlkvMFNXQUNLRlR5MEVXTGxQYUxhVkNnUzlSUVFleXFHeThXbVhObisxYXBxdWlwUk1Ga1p5b3RGejgiLCJtYWMiOiI5NmJkODgzMTc0MjBhODllMDQwNGY5YjBlNGNkODk0NGMyYTlkYmE1NTBiMjBiYjg0MTE1Njg3OGE0ODc1N2U0IiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6Ii9KN0h0V2xWeHhUYk1zZEVxL3lCV3c9PSIsInZhbHVlIjoiQjhPeWxYeVl1dTgva1NQMmtZbTlhQitHUTB4WHU1TUMrREdJblVSOWQ4amNGTS9CeVdpT294TUExcG1PQ2s5MXRub3p0cm4vdEFlZ3VtZ0pmMDNSNVFLeFR6M3hkZlFjUXVXS0tlSUNmUFAxVnVBR0dZZDc1aTVFS2Yra1VaKzIiLCJtYWMiOiIzYzI4MzgwNmY5MmI2ZWU1ODFiN2I1ODRlY2VkYTc5MDZiNDliYWRiNTRiMmZjOWUwNzhjNmVjZDc3ZmQzNmMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863533150\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1602216933 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InlDZjZ6OWxNSnFWcmdKZUJxRFNmQ2c9PSIsInZhbHVlIjoiYjhpVVFSbGJKSzBadzY4YVpoY0VPNUREVXNsN1A1VGZBWk9tRDcrZEtBUlFxdHZyQk1FYXVaQ05RMzYyVGVWZkpKczExNlkvMFNXQUNLRlR5MEVXTGxQYUxhVkNnUzlSUVFleXFHeThXbVhObisxYXBxdWlwUk1Ga1p5b3RGejgiLCJtYWMiOiI5NmJkODgzMTc0MjBhODllMDQwNGY5YjBlNGNkODk0NGMyYTlkYmE1NTBiMjBiYjg0MTE1Njg3OGE0ODc1N2U0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ii9KN0h0V2xWeHhUYk1zZEVxL3lCV3c9PSIsInZhbHVlIjoiQjhPeWxYeVl1dTgva1NQMmtZbTlhQitHUTB4WHU1TUMrREdJblVSOWQ4amNGTS9CeVdpT294TUExcG1PQ2s5MXRub3p0cm4vdEFlZ3VtZ0pmMDNSNVFLeFR6M3hkZlFjUXVXS0tlSUNmUFAxVnVBR0dZZDc1aTVFS2Yra1VaKzIiLCJtYWMiOiIzYzI4MzgwNmY5MmI2ZWU1ODFiN2I1ODRlY2VkYTc5MDZiNDliYWRiNTRiMmZjOWUwNzhjNmVjZDc3ZmQzNmMwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602216933\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2129605079 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 10:40:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129605079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-614682030 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-614682030\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}