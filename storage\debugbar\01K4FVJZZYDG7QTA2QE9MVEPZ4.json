{"__meta": {"id": "01K4FVJZZYDG7QTA2QE9MVEPZ4", "datetime": "2025-09-06 15:51:14", "utime": **********.687992, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[15:51:14] LOG.error: Failed to fetch notifications: Request failed with status code 401 AxiosError AxiosError: Request failed with status code 401\n    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)\n    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)\n    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)\n    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest ZAIGvyycXz7rL57DiaeiqhFi8LaRaHr1GKIMaozb get /notifications/dropdown true ERR_BAD_REQUEST 401 {\n    \"url\": \"http:\\/\\/localhost:8000\\/projects\\/create\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-09-06T15:51:09.317Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.63659, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.199624, "end": **********.688021, "duration": 0.48839688301086426, "duration_str": "488ms", "measures": [{"label": "Booting", "start": **********.199624, "relative_start": 0, "end": **********.548891, "relative_end": **********.548891, "duration": 0.*****************, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.548933, "relative_start": 0.*****************, "end": **********.688025, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.623654, "relative_start": 0.****************, "end": **********.62768, "relative_end": **********.62768, "duration": 0.004026174545288086, "duration_str": "4.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.678555, "relative_start": 0.****************, "end": **********.682917, "relative_end": **********.682917, "duration": 0.0043621063232421875, "duration_str": "4.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.682952, "relative_start": 0.*****************, "end": **********.682988, "relative_end": **********.682988, "duration": 3.600120544433594e-05, "duration_str": "36μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1154}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 972}], "start": **********.666984, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=101\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:101-127</a>", "duration": "491ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-254506303 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-254506303\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-09-06T15:51:09.317Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">Failed to fetch notifications:</span>\"\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Request failed with status code 401</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AxiosError</span>\"\n          \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n            <span class=sf-dump-str title=\"465 characters\">AxiosError: Request failed with status code 401<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n            <span class=sf-dump-str title=\"465 characters\">    at settle (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1253:12)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n            <span class=sf-dump-str title=\"465 characters\">    at XMLHttpRequest.onloadend (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1585:7)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n            <span class=sf-dump-str title=\"465 characters\">    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n            <span class=sf-dump-str title=\"465 characters\">    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24)</span>\n            \"\"\"\n          \"<span class=sf-dump-key>config</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>transitional</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>silentJSONParsing</span>\" => <span class=sf-dump-const>true</span>\n              \"<span class=sf-dump-key>forcedJSONParsing</span>\" => <span class=sf-dump-const>true</span>\n              \"<span class=sf-dump-key>clarifyTimeoutError</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            \"<span class=sf-dump-key>adapter</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">xhr</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">fetch</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>transformRequest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n            </samp>]\n            \"<span class=sf-dump-key>transformResponse</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n            </samp>]\n            \"<span class=sf-dump-key>timeout</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>xsrfCookieName</span>\" => \"<span class=sf-dump-str title=\"10 characters\">XSRF-TOKEN</span>\"\n            \"<span class=sf-dump-key>xsrfHeaderName</span>\" => \"<span class=sf-dump-str title=\"12 characters\">X-XSRF-TOKEN</span>\"\n            \"<span class=sf-dump-key>maxContentLength</span>\" => <span class=sf-dump-num>-1</span>\n            \"<span class=sf-dump-key>maxBodyLength</span>\" => <span class=sf-dump-num>-1</span>\n            \"<span class=sf-dump-key>env</span>\" => []\n            \"<span class=sf-dump-key>headers</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>Accept</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n              \"<span class=sf-dump-key>X-Requested-With</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n              \"<span class=sf-dump-key>X-CSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZAIGvyycXz7rL57DiaeiqhFi8LaRaHr1GKIMaozb</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n            \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/notifications/dropdown</span>\"\n            \"<span class=sf-dump-key>allowAbsoluteUrls</span>\" => <span class=sf-dump-const>true</span>\n          </samp>]\n          \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"15 characters\">ERR_BAD_REQUEST</span>\"\n          \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>401</span>\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/projects/create</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-698629425 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1407</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6ImNiTElPajV1S2RYYWx0UzkrTGhsZ0E9PSIsInZhbHVlIjoiZENLV3poK2h0MytJT0J6Tnc1bWxBNk1kckdXRTNTeWdqUWJjajFkR0ZhNmdHd25PQ09lT0xxVWQ1UGtwQytqTmhRSVBpNjhTdDlxeWVYZlA0L2pLaG9DRjRWVmZoMitobzBOT2Y2R0w1ZG1tSnRsaGNmVVE3ZVFSRUMwd1kySzQiLCJtYWMiOiJiMTQ3ZWQ0ZTQyOGY2ZTgzYzE0ZDU4NTAyZTc0ZjQwYTk4YmIyOGJiY2JkZTFjYjFmZDRjMTFhZWUzNDY5OTllIiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6ImUyVklBN21pWEpQOE0vNm91UVRreEE9PSIsInZhbHVlIjoidlNlWFhHczJZUWtFbW95UUFhSkxMT1RyOHZ3dG1PVzIwYU9pR3NJQzFDTDhqT1hYK3ZIRTQ3YzU0TWZUNkZzbFFXKzdkMEpGNGJMaFhjdnFoell5aC9ZdlRLNFRDVHdHUytKZGhYRzNxVXFUTU1xMVRvMGRrWUsxc3FkYlE4ZVoiLCJtYWMiOiIzZTIxYzI0Y2UwYzVlYTFjYzhkNWZlYTM0ODdkOGU1MjZlNzE3N2Y4ZDFjMDlhYjFmOTBhZGM4MzE3OGRjOGNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-698629425\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1817433293 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImNiTElPajV1S2RYYWx0UzkrTGhsZ0E9PSIsInZhbHVlIjoiZENLV3poK2h0MytJT0J6Tnc1bWxBNk1kckdXRTNTeWdqUWJjajFkR0ZhNmdHd25PQ09lT0xxVWQ1UGtwQytqTmhRSVBpNjhTdDlxeWVYZlA0L2pLaG9DRjRWVmZoMitobzBOT2Y2R0w1ZG1tSnRsaGNmVVE3ZVFSRUMwd1kySzQiLCJtYWMiOiJiMTQ3ZWQ0ZTQyOGY2ZTgzYzE0ZDU4NTAyZTc0ZjQwYTk4YmIyOGJiY2JkZTFjYjFmZDRjMTFhZWUzNDY5OTllIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImUyVklBN21pWEpQOE0vNm91UVRreEE9PSIsInZhbHVlIjoidlNlWFhHczJZUWtFbW95UUFhSkxMT1RyOHZ3dG1PVzIwYU9pR3NJQzFDTDhqT1hYK3ZIRTQ3YzU0TWZUNkZzbFFXKzdkMEpGNGJMaFhjdnFoell5aC9ZdlRLNFRDVHdHUytKZGhYRzNxVXFUTU1xMVRvMGRrWUsxc3FkYlE4ZVoiLCJtYWMiOiIzZTIxYzI0Y2UwYzVlYTFjYzhkNWZlYTM0ODdkOGU1MjZlNzE3N2Y4ZDFjMDlhYjFmOTBhZGM4MzE3OGRjOGNjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817433293\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1721751183 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 15:51:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721751183\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1790619112 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1790619112\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}