import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { DollarSign } from 'lucide-react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number;
    phone?: string;
    location?: string;
    bio?: string;
    skills?: string[];
    education?: string;
    created_at: string;
    projects: any[];
    bids: any[];
    wallet_transactions: any[];
}

interface EditDialogsProps {
    user: User;
    showEditDialog: boolean;
    showStatusDialog: boolean;
    showWalletDialog: boolean;
    showDeleteDialog: boolean;
    editData: {
        name: string;
        email: string;
        role: string;
        status: string;
        is_verified: boolean;
        bio: string;
        phone: string;
        location: string;
        skills: string[];
        education: string;
    };
    statusData: {
        status: string;
        reason: string;
    };
    walletData: {
        amount: string;
        type: string;
        reason: string;
    };
    editErrors: any;
    statusErrors: any;
    walletErrors: any;
    editProcessing: boolean;
    statusProcessing: boolean;
    walletProcessing: boolean;
    deleteProcessing: boolean;
    setEditData: (key: any, value: any) => void;
    setStatusData: (key: any, value: any) => void;
    setWalletData: (key: any, value: any) => void;
    onEditClose: () => void;
    onStatusClose: () => void;
    onWalletClose: () => void;
    onDeleteClose: () => void;
    onEdit: () => void;
    onStatusUpdate: () => void;
    onWalletAdjustment: () => void;
    onDelete: () => void;
}

export default function EditDialogs({
    user,
    showEditDialog,
    showStatusDialog,
    showWalletDialog,
    showDeleteDialog,
    editData,
    statusData,
    walletData,
    editErrors,
    statusErrors,
    walletErrors,
    editProcessing,
    statusProcessing,
    walletProcessing,
    deleteProcessing,
    setEditData,
    setStatusData,
    setWalletData,
    onEditClose,
    onStatusClose,
    onWalletClose,
    onDeleteClose,
    onEdit,
    onStatusUpdate,
    onWalletAdjustment,
    onDelete,
}: EditDialogsProps) {
    return (
        <>
            {/* Edit User Dialog */}
            <Dialog open={showEditDialog} onOpenChange={onEditClose}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                        <DialogDescription>Update user information and permissions.</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="name">Name</Label>
                                <Input id="name" value={editData.name} onChange={(e) => setEditData('name', e.target.value)} />
                                {editErrors.name && <p className="text-sm text-destructive">{editErrors.name}</p>}
                            </div>
                            <div>
                                <Label htmlFor="email">Email</Label>
                                <Input id="email" type="email" value={editData.email} onChange={(e) => setEditData('email', e.target.value)} />
                                {editErrors.email && <p className="text-sm text-destructive">{editErrors.email}</p>}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="role">Role</Label>
                                <Select value={editData.role} onValueChange={(value: 'user' | 'admin' | 'super_admin') => setEditData('role', value)}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="user">User</SelectItem>
                                        <SelectItem value="admin">Admin</SelectItem>
                                        <SelectItem value="super_admin">Super Admin</SelectItem>
                                    </SelectContent>
                                </Select>
                                {editErrors.role && <p className="text-sm text-destructive">{editErrors.role}</p>}
                            </div>
                            <div>
                                <Label htmlFor="status">Status</Label>
                                <Select
                                    value={editData.status}
                                    onValueChange={(value: 'active' | 'suspended' | 'blocked') => setEditData('status', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="suspended">Suspended</SelectItem>
                                        <SelectItem value="blocked">Blocked</SelectItem>
                                    </SelectContent>
                                </Select>
                                {editErrors.status && <p className="text-sm text-destructive">{editErrors.status}</p>}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="phone">Phone</Label>
                                <Input id="phone" value={editData.phone} onChange={(e) => setEditData('phone', e.target.value)} />
                                {editErrors.phone && <p className="text-sm text-destructive">{editErrors.phone}</p>}
                            </div>
                            <div>
                                <Label htmlFor="location">Location</Label>
                                <Input id="location" value={editData.location} onChange={(e) => setEditData('location', e.target.value)} />
                                {editErrors.location && <p className="text-sm text-destructive">{editErrors.location}</p>}
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="education">Education</Label>
                            <Input id="education" value={editData.education} onChange={(e) => setEditData('education', e.target.value)} />
                            {editErrors.education && <p className="text-sm text-destructive">{editErrors.education}</p>}
                        </div>
                        <div>
                            <Label htmlFor="bio">Bio</Label>
                            <Textarea id="bio" value={editData.bio} onChange={(e) => setEditData('bio', e.target.value)} rows={3} />
                            {editErrors.bio && <p className="text-sm text-destructive">{editErrors.bio}</p>}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={onEditClose}>
                            Cancel
                        </Button>
                        <Button onClick={onEdit} disabled={editProcessing}>
                            {editProcessing ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Status Update Dialog */}
            <Dialog open={showStatusDialog} onOpenChange={onStatusClose}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Update User Status</DialogTitle>
                        <DialogDescription>Change the user's account status.</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div>
                            <Label htmlFor="status">Status</Label>
                            <Select
                                value={statusData.status}
                                onValueChange={(value: 'active' | 'suspended' | 'blocked') => setStatusData('status', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="suspended">Suspended</SelectItem>
                                    <SelectItem value="blocked">Blocked</SelectItem>
                                </SelectContent>
                            </Select>
                            {statusErrors.status && <p className="text-sm text-destructive">{statusErrors.status}</p>}
                        </div>
                        <div>
                            <Label htmlFor="reason">Reason (Optional)</Label>
                            <Textarea
                                id="reason"
                                value={statusData.reason}
                                onChange={(e) => setStatusData('reason', e.target.value)}
                                rows={3}
                                placeholder="Reason for status change..."
                            />
                            {statusErrors.reason && <p className="text-sm text-destructive">{statusErrors.reason}</p>}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={onStatusClose}>
                            Cancel
                        </Button>
                        <Button onClick={onStatusUpdate} disabled={statusProcessing}>
                            {statusProcessing ? 'Updating...' : 'Update Status'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Wallet Adjustment Dialog */}
            <Dialog open={showWalletDialog} onOpenChange={onWalletClose}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Adjust Wallet Balance</DialogTitle>
                        <DialogDescription>Add or subtract funds from the user's wallet.</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div>
                            <Label htmlFor="type">Action</Label>
                            <Select value={walletData.type} onValueChange={(value: 'subtract') => setWalletData('type', value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="subtract">Subtract Funds</SelectItem>
                                </SelectContent>
                            </Select>
                            {walletErrors.type && <p className="text-sm text-destructive">{walletErrors.type}</p>}
                        </div>
                        <div>
                            <Label htmlFor="amount">Amount</Label>
                            <Input
                                id="amount"
                                type="number"
                                step="0.01"
                                value={walletData.amount}
                                onChange={(e) => setWalletData('amount', e.target.value)}
                                placeholder="0.00"
                            />
                            {walletErrors.amount && <p className="text-sm text-destructive">{walletErrors.amount}</p>}
                        </div>
                        <div>
                            <Label htmlFor="wallet-reason">Reason</Label>
                            <Textarea
                                id="wallet-reason"
                                value={walletData.reason}
                                onChange={(e) => setWalletData('reason', e.target.value)}
                                rows={3}
                                placeholder="Reason for wallet adjustment..."
                            />
                            {walletErrors.reason && <p className="text-sm text-destructive">{walletErrors.reason}</p>}
                        </div>
                        <Alert>
                            <DollarSign className="h-4 w-4" />
                            <AlertDescription>Current balance: ₵{user.wallet_balance}</AlertDescription>
                        </Alert>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={onWalletClose}>
                            Cancel
                        </Button>
                        <Button onClick={onWalletAdjustment} disabled={walletProcessing}>
                            {walletProcessing ? 'Processing...' : 'Adjust Balance'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={showDeleteDialog} onOpenChange={onDeleteClose}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete User</DialogTitle>
                        <DialogDescription>Are you sure you want to delete this user? This action cannot be undone.</DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={onDeleteClose}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={onDelete} disabled={deleteProcessing}>
                            {deleteProcessing ? 'Deleting...' : 'Delete User'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
}
