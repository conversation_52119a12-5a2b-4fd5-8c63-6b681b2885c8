{"__meta": {"id": "01K4FYQ16PBMRFPWJ4FWK9MSXJ", "datetime": "2025-09-06 16:45:52", "utime": **********.730515, "method": "GET", "uri": "/admin/analytics", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.222974, "end": **********.730558, "duration": 1.5075838565826416, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": **********.222974, "relative_start": 0, "end": **********.574088, "relative_end": **********.574088, "duration": 0.*****************, "duration_str": "351ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.574106, "relative_start": 0.****************, "end": **********.730563, "relative_end": 5.0067901611328125e-06, "duration": 1.****************, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.605153, "relative_start": 0.*****************, "end": **********.611289, "relative_end": **********.611289, "duration": 0.0061359405517578125, "duration_str": "6.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.671913, "relative_start": 1.****************, "end": **********.722217, "relative_end": **********.722217, "duration": 0.*****************, "duration_str": "50.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Admin/Analytics", "param_count": null, "params": [], "start": **********.730153, "type": "tsx", "hash": "tsxC:\\dev\\thesylink\\resources\\js/Pages/Admin/Analytics.tsxAdmin/Analytics", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fresources%2Fjs%2Fpages%2FAdmin%2FAnalytics.tsx&line=1", "ajax": false, "filename": "Analytics.tsx", "line": "?"}}]}, "queries": {"count": 45, "nb_statements": 44, "nb_visible_statements": 45, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.76324, "accumulated_duration_str": "763ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.649985, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'bTb3puOWP2D7dXmSq7EecVFTSI005xt6cljVQPYj' limit 1", "type": "query", "params": [], "bindings": ["bTb3puOWP2D7dXmSq7EecVFTSI005xt6cljVQPYj"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.662725, "duration": 0.30847, "duration_str": "308ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 40.416}, {"sql": "select * from \"users\" where \"id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' limit 1", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.01893, "duration": 0.12355, "duration_str": "124ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 40.416, "width_percent": 16.188}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.168495, "duration": 0.012199999999999999, "duration_str": "12.2ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:32", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=32", "ajax": false, "filename": "AnalyticsController.php", "line": "32"}, "connection": "thesylink", "explain": null, "start_percent": 56.603, "width_percent": 1.598}, {"sql": "select count(*) as aggregate from \"users\" where extract(month from \"created_at\") = '09'", "type": "query", "params": [], "bindings": ["09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.183872, "duration": 0.02702, "duration_str": "27.02ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:33", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=33", "ajax": false, "filename": "AnalyticsController.php", "line": "33"}, "connection": "thesylink", "explain": null, "start_percent": 58.202, "width_percent": 3.54}, {"sql": "select count(*) as aggregate from \"users\" where \"status\" = 'active'", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.2135608, "duration": 0.005690000000000001, "duration_str": "5.69ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:34", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=34", "ajax": false, "filename": "AnalyticsController.php", "line": "34"}, "connection": "thesylink", "explain": null, "start_percent": 61.742, "width_percent": 0.746}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.2245631, "duration": 0.00522, "duration_str": "5.22ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:35", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=35", "ajax": false, "filename": "AnalyticsController.php", "line": "35"}, "connection": "thesylink", "explain": null, "start_percent": 62.488, "width_percent": 0.684}, {"sql": "select \"role\", count(*) as count from \"users\" group by \"role\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.239246, "duration": 0.00639, "duration_str": "6.39ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:38", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=38", "ajax": false, "filename": "AnalyticsController.php", "line": "38"}, "connection": "thesylink", "explain": null, "start_percent": 63.171, "width_percent": 0.837}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.2510111, "duration": 0.02555, "duration_str": "25.55ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:44", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=44", "ajax": false, "filename": "AnalyticsController.php", "line": "44"}, "connection": "thesylink", "explain": null, "start_percent": 64.009, "width_percent": 3.348}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'open'", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.279505, "duration": 0.009779999999999999, "duration_str": "9.78ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=45", "ajax": false, "filename": "AnalyticsController.php", "line": "45"}, "connection": "thesylink", "explain": null, "start_percent": 67.356, "width_percent": 1.281}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'in_progress'", "type": "query", "params": [], "bindings": ["in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.293313, "duration": 0.00791, "duration_str": "7.91ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:46", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=46", "ajax": false, "filename": "AnalyticsController.php", "line": "46"}, "connection": "thesylink", "explain": null, "start_percent": 68.638, "width_percent": 1.036}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.304333, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=47", "ajax": false, "filename": "AnalyticsController.php", "line": "47"}, "connection": "thesylink", "explain": null, "start_percent": 69.674, "width_percent": 0.616}, {"sql": "select \"category\", count(*) as count from \"projects\" where \"category\" is not null group by \"category\" order by \"count\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 53}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.313465, "duration": 0.0065899999999999995, "duration_str": "6.59ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:53", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=53", "ajax": false, "filename": "AnalyticsController.php", "line": "53"}, "connection": "thesylink", "explain": null, "start_percent": 70.29, "width_percent": 0.863}, {"sql": "select \"academic_level\", count(*) as count from \"projects\" where \"academic_level\" is not null group by \"academic_level\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 58}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.323196, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:58", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=58", "ajax": false, "filename": "AnalyticsController.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 71.153, "width_percent": 0.649}, {"sql": "select sum(\"wallet_balance\") as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.331811, "duration": 0.008289999999999999, "duration_str": "8.29ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:66", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=66", "ajax": false, "filename": "AnalyticsController.php", "line": "66"}, "connection": "thesylink", "explain": null, "start_percent": 71.802, "width_percent": 1.086}, {"sql": "select count(*) as aggregate from \"wallet_transactions\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.345493, "duration": 0.01495, "duration_str": "14.95ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:67", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=67", "ajax": false, "filename": "AnalyticsController.php", "line": "67"}, "connection": "thesylink", "explain": null, "start_percent": 72.888, "width_percent": 1.959}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'deposit' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["deposit", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.364593, "duration": 0.00504, "duration_str": "5.04ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=68", "ajax": false, "filename": "AnalyticsController.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 74.847, "width_percent": 0.66}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["withdrawal", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.373163, "duration": 0.004719999999999999, "duration_str": "4.72ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:69", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=69", "ajax": false, "filename": "AnalyticsController.php", "line": "69"}, "connection": "thesylink", "explain": null, "start_percent": 75.507, "width_percent": 0.618}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["withdrawal", "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 70}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.379882, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:70", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=70", "ajax": false, "filename": "AnalyticsController.php", "line": "70"}, "connection": "thesylink", "explain": null, "start_percent": 76.125, "width_percent": 0.573}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"status\" = 'collected'", "type": "query", "params": [], "bindings": ["collected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 73}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.391345, "duration": 0.015460000000000002, "duration_str": "15.46ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:73", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=73", "ajax": false, "filename": "AnalyticsController.php", "line": "73"}, "connection": "thesylink", "explain": null, "start_percent": 76.698, "width_percent": 2.026}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.410193, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:74", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=74", "ajax": false, "filename": "AnalyticsController.php", "line": "74"}, "connection": "thesylink", "explain": null, "start_percent": 78.724, "width_percent": 0.597}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"withdrawn_at\" is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.418037, "duration": 0.009460000000000001, "duration_str": "9.46ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:75", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=75", "ajax": false, "filename": "AnalyticsController.php", "line": "75"}, "connection": "thesylink", "explain": null, "start_percent": 79.321, "width_percent": 1.239}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"status\" = 'collected' and \"withdrawn_at\" is null", "type": "query", "params": [], "bindings": ["collected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.430416, "duration": 0.00491, "duration_str": "4.91ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:76", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=76", "ajax": false, "filename": "AnalyticsController.php", "line": "76"}, "connection": "thesylink", "explain": null, "start_percent": 80.561, "width_percent": 0.643}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END) as deposits, SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END) as withdrawals from \"wallet_transactions\" where extract(year from \"created_at\") = 2025 group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": [2025], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.438958, "duration": 0.019129999999999998, "duration_str": "19.13ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:86", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=86", "ajax": false, "filename": "AnalyticsController.php", "line": "86"}, "connection": "thesylink", "explain": null, "start_percent": 81.204, "width_percent": 2.506}, {"sql": "select TO_CHAR(collected_at, 'YYYY-MM') as month, SUM(amount) as total_commission from \"platform_commissions\" where \"status\" = 'collected' and extract(year from \"collected_at\") = 2025 group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["collected", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.467364, "duration": 0.00607, "duration_str": "6.07ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:97", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=97", "ajax": false, "filename": "AnalyticsController.php", "line": "97"}, "connection": "thesylink", "explain": null, "start_percent": 83.71, "width_percent": 0.795}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 104}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.477403, "duration": 0.01692, "duration_str": "16.92ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:104", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=104", "ajax": false, "filename": "AnalyticsController.php", "line": "104"}, "connection": "thesylink", "explain": null, "start_percent": 84.506, "width_percent": 2.217}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.497235, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:105", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=105", "ajax": false, "filename": "AnalyticsController.php", "line": "105"}, "connection": "thesylink", "explain": null, "start_percent": 86.722, "width_percent": 0.541}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.504273, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=106", "ajax": false, "filename": "AnalyticsController.php", "line": "106"}, "connection": "thesylink", "explain": null, "start_percent": 87.264, "width_percent": 0.597}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'rejected'", "type": "query", "params": [], "bindings": ["rejected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.511168, "duration": 0.0114, "duration_str": "11.4ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:107", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=107", "ajax": false, "filename": "AnalyticsController.php", "line": "107"}, "connection": "thesylink", "explain": null, "start_percent": 87.861, "width_percent": 1.494}, {"sql": "select avg(\"amount\") as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 112}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5252242, "duration": 0.00625, "duration_str": "6.25ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:112", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=112", "ajax": false, "filename": "AnalyticsController.php", "line": "112"}, "connection": "thesylink", "explain": null, "start_percent": 89.355, "width_percent": 0.819}, {"sql": "select \"id\", \"name\", \"email\", \"created_at\", \"role\" from \"users\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.533426, "duration": 0.00644, "duration_str": "6.44ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:117", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=117", "ajax": false, "filename": "AnalyticsController.php", "line": "117"}, "connection": "thesylink", "explain": null, "start_percent": 90.173, "width_percent": 0.844}, {"sql": "select \"id\", \"title\", \"user_id\", \"status\", \"created_at\" from \"projects\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.543936, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:118", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=118", "ajax": false, "filename": "AnalyticsController.php", "line": "118"}, "connection": "thesylink", "explain": null, "start_percent": 91.017, "width_percent": 0.718}, {"sql": "select \"id\", \"name\" from \"users\" where \"users\".\"id\" in ('01991e3e-2cd2-7050-ae72-4e0beef93fb6', '01991e3e-2dde-736d-982b-0cac6a8d8141', '01991e3e-2f04-704f-b98a-ab1d99f5cc76')", "type": "query", "params": [], "bindings": ["01991e3e-2cd2-7050-ae72-4e0beef93fb6", "01991e3e-2dde-736d-982b-0cac6a8d8141", "01991e3e-2f04-704f-b98a-ab1d99f5cc76"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.558554, "duration": 0.00584, "duration_str": "5.84ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:118", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=118", "ajax": false, "filename": "AnalyticsController.php", "line": "118"}, "connection": "thesylink", "explain": null, "start_percent": 91.735, "width_percent": 0.765}, {"sql": "select \"id\", \"user_id\", \"type\", \"amount\", \"status\", \"created_at\" from \"wallet_transactions\" order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 126}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.567739, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:126", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=126", "ajax": false, "filename": "AnalyticsController.php", "line": "126"}, "connection": "thesylink", "explain": null, "start_percent": 92.5, "width_percent": 0.639}, {"sql": "select \"id\", \"name\" from \"users\" where \"users\".\"id\" in ('01991e3e-2ac7-73a5-b86e-ba67f54988d5')", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 126}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.576417, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:126", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=126", "ajax": false, "filename": "AnalyticsController.php", "line": "126"}, "connection": "thesylink", "explain": null, "start_percent": 93.14, "width_percent": 0.54}, {"sql": "select \"id\", \"project_id\", \"milestone_id\", \"amount\", \"status\", \"collected_at\" from \"platform_commissions\" order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5841238, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:131", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=131", "ajax": false, "filename": "AnalyticsController.php", "line": "131"}, "connection": "thesylink", "explain": null, "start_percent": 93.68, "width_percent": 0.63}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"users\" where \"created_at\" >= '2024-09-06 16:45:52' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 16:45:52"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 143}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.594018, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:143", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=143", "ajax": false, "filename": "AnalyticsController.php", "line": "143"}, "connection": "thesylink", "explain": null, "start_percent": 94.31, "width_percent": 0.711}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"projects\" where \"created_at\" >= '2024-09-06 16:45:52' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 16:45:52"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 152}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.6024868, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:152", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=152", "ajax": false, "filename": "AnalyticsController.php", "line": "152"}, "connection": "thesylink", "explain": null, "start_percent": 95.021, "width_percent": 0.651}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.612277, "duration": 0.00466, "duration_str": "4.66ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:158", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=158", "ajax": false, "filename": "AnalyticsController.php", "line": "158"}, "connection": "thesylink", "explain": null, "start_percent": 95.672, "width_percent": 0.611}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.620858, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:158", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=158", "ajax": false, "filename": "AnalyticsController.php", "line": "158"}, "connection": "thesylink", "explain": null, "start_percent": 96.283, "width_percent": 0.58}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.628147, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:159", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=159", "ajax": false, "filename": "AnalyticsController.php", "line": "159"}, "connection": "thesylink", "explain": null, "start_percent": 96.863, "width_percent": 0.54}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.635795, "duration": 0.00647, "duration_str": "6.47ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:159", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=159", "ajax": false, "filename": "AnalyticsController.php", "line": "159"}, "connection": "thesylink", "explain": null, "start_percent": 97.403, "width_percent": 0.848}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.6456091, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:160", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=160", "ajax": false, "filename": "AnalyticsController.php", "line": "160"}, "connection": "thesylink", "explain": null, "start_percent": 98.251, "width_percent": 0.595}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.6539838, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:160", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=160", "ajax": false, "filename": "AnalyticsController.php", "line": "160"}, "connection": "thesylink", "explain": null, "start_percent": 98.846, "width_percent": 0.604}, {"sql": "select avg(\"accepted_bid_amount\") as aggregate from \"projects\" where \"accepted_bid_amount\" is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 165}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.661198, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:165", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=165", "ajax": false, "filename": "AnalyticsController.php", "line": "165"}, "connection": "thesylink", "explain": null, "start_percent": 99.45, "width_percent": 0.55}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\WalletTransaction": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FWalletTransaction.php&line=1", "ajax": false, "filename": "WalletTransaction.php", "line": "?"}}, "App\\Models\\Project": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}}, "count": 23, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 23}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index", "uri": "GET admin/analytics", "controller": "App\\Http\\Controllers\\Admin\\AnalyticsController@index<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/AnalyticsController.php:21-184</a>", "middleware": "web, auth, verified, admin", "duration": "1.52s", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-994874076 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-994874076\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-434801190 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-434801190\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-580059991 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PQDz1p7esX9LCfNX5BamqtnrCnfY5t2YJn7urEbJ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5md1F6YytudHNUUzZRSVBtcFQzRFE9PSIsInZhbHVlIjoiMTFZVDh3T3Q0Nm55ZW1tYjViSnV6eUJkZ01uOVl6NW9zNXVLYnFtS2tCYTJiY0VZUUZTUUZFbk1ONjNoV3kxSzJUdmZCcituVkNkNVRWRWU3NDdmb1N3ZCtCbzh4U216Z052UERZdDZia1ZkbzBzVEM0Q0Uxalc1eE54WkxraE4iLCJtYWMiOiJlMDlmYWRhZTZjM2NjYmZlYWM5ZGZhZWU3MmJkMTMxZTQ2MTFjYTZmNjdkYWIwMjE4YTYyNWFkYjNlZDcwNjAwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6Ik5md1F6YytudHNUUzZRSVBtcFQzRFE9PSIsInZhbHVlIjoiMTFZVDh3T3Q0Nm55ZW1tYjViSnV6eUJkZ01uOVl6NW9zNXVLYnFtS2tCYTJiY0VZUUZTUUZFbk1ONjNoV3kxSzJUdmZCcituVkNkNVRWRWU3NDdmb1N3ZCtCbzh4U216Z052UERZdDZia1ZkbzBzVEM0Q0Uxalc1eE54WkxraE4iLCJtYWMiOiJlMDlmYWRhZTZjM2NjYmZlYWM5ZGZhZWU3MmJkMTMxZTQ2MTFjYTZmNjdkYWIwMjE4YTYyNWFkYjNlZDcwNjAwIiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6Ii9CYkVvenBHWkgzY1lRVWxPUWRraVE9PSIsInZhbHVlIjoiZG11RFREcy95S0JBdEVlVXFIMmxJcTdvNmJpYTZ6WEtOdkpqSDdVeW9OenoxdC9pT0drbVdEWWhqUDArWWZBQ0lac3VtN3BCdTR2aUg2QUZoT2ZNSHV3WVAxOUdVUzVEYXFaVFNBMjZ1ZEpqWDZNSXBUK2NHV0ZQMnIvOFJEcXYiLCJtYWMiOiI5MTJhNDUxMDhmNTg1YTcyNWViZDc2NmI2ZTRiM2EzZmJjNzA2YTU4OWFjZTY4YjU1ZTJmMTM4MDMwMTZkNjgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580059991\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-499606752 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PQDz1p7esX9LCfNX5BamqtnrCnfY5t2YJn7urEbJ</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bTb3puOWP2D7dXmSq7EecVFTSI005xt6cljVQPYj</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499606752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-285967576 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 16:45:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285967576\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-495998398 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PQDz1p7esX9LCfNX5BamqtnrCnfY5t2YJn7urEbJ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">01991e3e-2ac7-73a5-b86e-ba67f54988d5</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495998398\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index"}, "badge": null}}