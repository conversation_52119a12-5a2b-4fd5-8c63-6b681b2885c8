<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProjectRequest;
use App\Models\Project;
use App\Models\ProjectFile;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ProjectController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Project::with(['user', 'files'])
            ->where('user_id', '!=', Auth::id())
            ->where('status', 'open');

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                    ->orWhere('description', 'like', "%{$searchTerm}%")
                    ->orWhere('requirements', 'like', "%{$searchTerm}%")
                    ->orWhere('category', 'like', "%{$searchTerm}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }

        // Academic level filter
        if ($request->filled('academic_level')) {
            $query->where('academic_level', $request->get('academic_level'));
        }

        // Budget range filter
        if ($request->filled('budget_range')) {
            $budgetRange = explode('-', $request->get('budget_range'));
            if (count($budgetRange) === 2) {
                $minBudget = (float) $budgetRange[0];
                $maxBudget = (float) $budgetRange[1];

                $query->where(function ($q) use ($minBudget, $maxBudget) {
                    $q->where(function ($subQ) use ($minBudget, $maxBudget) {
                        $subQ->where('budget_min', '>=', $minBudget)
                            ->where('budget_min', '<=', $maxBudget);
                    })->orWhere(function ($subQ) use ($minBudget, $maxBudget) {
                        $subQ->where('budget_max', '>=', $minBudget)
                            ->where('budget_max', '<=', $maxBudget);
                    })->orWhere(function ($subQ) use ($minBudget, $maxBudget) {
                        $subQ->where('budget_min', '<=', $minBudget)
                            ->where('budget_max', '>=', $maxBudget);
                    });
                });
            }
        }

        $projects = $query->latest()->paginate(12)->withQueryString();

        // Transform the projects to ensure budget values are properly cast
        $projects->getCollection()->transform(function ($project) {
            return [
                ...$project->toArray(),
                'budget_min' => $project->budget_min ? (float) $project->budget_min : null,
                'budget_max' => $project->budget_max ? (float) $project->budget_max : null,
            ];
        });

        return Inertia::render('projects/browse', [
            'projects' => $projects,
            'filters' => $request->only(['search', 'category', 'academic_level', 'budget_range']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('projects/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProjectRequest $request)
    {
        $validated = $request->validated();

        $project = Project::create([
            'user_id' => Auth::id(),
            ...$validated,
        ]);

        // Handle file uploads
        if ($request->hasFile('files')) {
            $fileCount = 0;
            foreach ($request->file('files') as $file) {
                $filename = time().'_'.$fileCount.'.'.$file->getClientOriginalExtension();
                $path = $file->storeAs('project-files', $filename, 'public');

                ProjectFile::create([
                    'project_id' => $project->id,
                    'filename' => $filename,
                    'original_name' => $file->getClientOriginalName(),
                    'file_path' => $path,
                    'mime_type' => $file->getMimeType(),
                    'file_size' => $file->getSize(),
                ]);

                $fileCount++;
            }

            $project->update(['file_count' => $fileCount]);
        }

        return redirect()->route('dashboard');
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $project)
    {
        $project->load(['user', 'files', 'bids.user', 'assignedFreelancer']);

        return Inertia::render('projects/show', [
            'project' => [
                ...$project->toArray(),
                'budget_min' => $project->budget_min ? (float) $project->budget_min : null,
                'budget_max' => $project->budget_max ? (float) $project->budget_max : null,
            ],
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project)
    {
        $this->authorize('update', $project);

        return Inertia::render('projects/edit', [
            'project' => [
                ...$project->load('files')->toArray(),
                'budget_min' => $project->budget_min ? (float) $project->budget_min : null,
                'budget_max' => $project->budget_max ? (float) $project->budget_max : null,
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $project)
    {
        $this->authorize('update', $project);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'nullable|string',
            'budget_min' => 'nullable|numeric|min:0',
            'budget_max' => 'nullable|numeric|min:0|gte:budget_min',
            'budget_type' => 'required|in:fixed,negotiable',
            'deadline' => 'nullable|date|after:today',
            'category' => 'nullable|string|max:255',
            'academic_level' => 'nullable|string|max:255',
            'status' => 'required|in:open,in_progress,completed,cancelled',
        ]);

        $project->update($validated);

        return redirect()->route('projects.show', $project)->with('success', 'Project updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project)
    {
        $this->authorize('delete', $project);

        // Delete associated files from storage
        foreach ($project->files as $file) {
            Storage::disk('public')->delete($file->file_path);
        }

        $project->delete();

        return redirect()->route('dashboard')->with('success', 'Project deleted successfully!');
    }

    /**
     * Download a project file (only for assigned freelancers and project owners)
     */
    public function downloadFile(Project $project, ProjectFile $file)
    {
        // Ensure the file belongs to this project
        if ($file->project_id !== $project->id) {
            abort(404, 'File not found.');
        }

        $user = Auth::user();

        // Check if user is authorized to download files
        // Only project owner or assigned freelancer can download
        if ($user->id !== $project->user_id && $user->id !== $project->assigned_freelancer_id) {
            abort(403, 'You are not authorized to download this file.');
        }

        // For assigned freelancers, ensure the project is actually assigned (not just open)
        if ($user->id === $project->assigned_freelancer_id && $project->status === 'open') {
            abort(403, 'You are not authorized to download this file until the project is assigned to you.');
        }

        // Check if file exists
        if (! Storage::disk('public')->exists($file->file_path)) {
            abort(404, 'File not found.');
        }

        // Return file download response
        return response()->download(
            Storage::disk('public')->path($file->file_path),
            $file->original_name
        );
    }
}
