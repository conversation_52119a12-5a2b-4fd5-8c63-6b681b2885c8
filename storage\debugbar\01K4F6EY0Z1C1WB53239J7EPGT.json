{"__meta": {"id": "01K4F6EY0Z1C1WB53239J7EPGT", "datetime": "2025-09-06 09:42:01", "utime": **********.50479, "method": "GET", "uri": "/analytics", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.133414, "end": **********.504808, "duration": 0.37139391899108887, "duration_str": "371ms", "measures": [{"label": "Booting", "start": **********.133414, "relative_start": 0, "end": **********.286761, "relative_end": **********.286761, "duration": 0.*****************, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.286776, "relative_start": 0.*****************, "end": **********.504816, "relative_end": 8.106231689453125e-06, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.306423, "relative_start": 0.*****************, "end": **********.309485, "relative_end": **********.309485, "duration": 0.003062009811401367, "duration_str": "3.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.480146, "relative_start": 0.*****************, "end": **********.502226, "relative_end": **********.502226, "duration": 0.022080183029174805, "duration_str": "22.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Analytics/Show", "param_count": null, "params": [], "start": **********.504678, "type": "tsx", "hash": "tsxC:\\dev\\thesylink\\resources\\js/Pages/Analytics/Show.tsxAnalytics/Show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fresources%2Fjs%2Fpages%2FAnalytics%2FShow.tsx&line=1", "ajax": false, "filename": "Show.tsx", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 11, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10929, "accumulated_duration_str": "109ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.323333, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy' limit 1", "type": "query", "params": [], "bindings": ["MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3305042, "duration": 0.05769, "duration_str": "57.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 52.786}, {"sql": "select * from \"users\" where \"id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' limit 1", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.3998048, "duration": 0.00816, "duration_str": "8.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 52.786, "width_percent": 7.466}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month,\nSUM(CASE WHEN type = 'credit' THEN amount ELSE 0 END) as earnings,\nSUM(CASE WHEN type = 'debit' THEN amount ELSE 0 END) as spending from \"wallet_transactions\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' group by \"month\" order by \"month\" desc limit 12", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 77}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.42173, "duration": 0.017, "duration_str": "17ms", "memory": 0, "memory_str": null, "filename": "UserController.php:77", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=77", "ajax": false, "filename": "UserController.php", "line": "77"}, "connection": "thesylink", "explain": null, "start_percent": 60.253, "width_percent": 15.555}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month,\nCOUNT(*) as total,\nSUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed from \"projects\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' group by \"month\" order by \"month\" desc limit 12", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 87}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.442019, "duration": 0.0065, "duration_str": "6.5ms", "memory": 0, "memory_str": null, "filename": "UserController.php:87", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=87", "ajax": false, "filename": "UserController.php", "line": "87"}, "connection": "thesylink", "explain": null, "start_percent": 75.807, "width_percent": 5.947}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month,\nCOUNT(*) as total_bids,\nSUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted_bids from \"bids\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' group by \"month\" order by \"month\" desc limit 12", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.4507868, "duration": 0.00621, "duration_str": "6.21ms", "memory": 0, "memory_str": null, "filename": "UserController.php:97", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=97", "ajax": false, "filename": "UserController.php", "line": "97"}, "connection": "thesylink", "explain": null, "start_percent": 81.755, "width_percent": 5.682}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' and \"type\" = 'credit'", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5", "credit"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 102}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.458547, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "UserController.php:102", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 102}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=102", "ajax": false, "filename": "UserController.php", "line": "102"}, "connection": "thesylink", "explain": null, "start_percent": 87.437, "width_percent": 2.69}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' and \"type\" = 'debit'", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5", "debit"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.462728, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "UserController.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=106", "ajax": false, "filename": "UserController.php", "line": "106"}, "connection": "thesylink", "explain": null, "start_percent": 90.127, "width_percent": 2.269}, {"sql": "select count(*) as aggregate from \"projects\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5'", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.466484, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "UserController.php:117", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=117", "ajax": false, "filename": "UserController.php", "line": "117"}, "connection": "thesylink", "explain": null, "start_percent": 92.396, "width_percent": 1.912}, {"sql": "select count(*) as aggregate from \"projects\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 118}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.469851, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "UserController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=118", "ajax": false, "filename": "UserController.php", "line": "118"}, "connection": "thesylink", "explain": null, "start_percent": 94.309, "width_percent": 1.976}, {"sql": "select count(*) as aggregate from \"bids\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5'", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.473177, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "UserController.php:119", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=119", "ajax": false, "filename": "UserController.php", "line": "119"}, "connection": "thesylink", "explain": null, "start_percent": 96.285, "width_percent": 1.894}, {"sql": "select count(*) as aggregate from \"bids\" where \"user_id\" = '01991e3e-2ac7-73a5-b86e-ba67f54988d5' and \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["01991e3e-2ac7-73a5-b86e-ba67f54988d5", "accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 120}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.4762938, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "UserController.php:120", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\UserController.php", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=120", "ajax": false, "filename": "UserController.php", "line": "120"}, "connection": "thesylink", "explain": null, "start_percent": 98.179, "width_percent": 1.821}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\WalletTransaction": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FWalletTransaction.php&line=1", "ajax": false, "filename": "WalletTransaction.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/analytics", "action_name": "user.analytics", "controller_action": "App\\Http\\Controllers\\UserController@analytics", "uri": "GET analytics", "controller": "App\\Http\\Controllers\\UserController@analytics<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=65\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FUserController.php&line=65\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/UserController.php:65-123</a>", "middleware": "web, auth, verified", "duration": "373ms", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1613581712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1613581712\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-89375019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-89375019\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-904725503 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjdQTXZWQnBVaHRVQlkwLzhaUk1TQnc9PSIsInZhbHVlIjoiNHFybTFTQWZuaGFVVTRxZjNudEwrMEdwQVBldk1UamdmNnc2ODNjYlA2bTRxNEhmcFRSSDlCVm80M1lLYjJFRTUzc0FiK1FRTGdmR1RxZ3hxb25UVVdOUXVKNURSTDlVUENDR3pQMndWR1psY2NSVUp6T1NUZVl1QmpVSDcvVE4iLCJtYWMiOiI4YjBkMmY3NGFhYjE2ZDRlOWRmYWJjMjU4NzMzYjQ3YjFiNjM5Y2RlZjRmNjU4MWQ2YzllMTA1ZTY1MGY4ZTc5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IjdQTXZWQnBVaHRVQlkwLzhaUk1TQnc9PSIsInZhbHVlIjoiNHFybTFTQWZuaGFVVTRxZjNudEwrMEdwQVBldk1UamdmNnc2ODNjYlA2bTRxNEhmcFRSSDlCVm80M1lLYjJFRTUzc0FiK1FRTGdmR1RxZ3hxb25UVVdOUXVKNURSTDlVUENDR3pQMndWR1psY2NSVUp6T1NUZVl1QmpVSDcvVE4iLCJtYWMiOiI4YjBkMmY3NGFhYjE2ZDRlOWRmYWJjMjU4NzMzYjQ3YjFiNjM5Y2RlZjRmNjU4MWQ2YzllMTA1ZTY1MGY4ZTc5IiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6IlA0OTZPOUlsVUFrMFJrK1c0NFdOa3c9PSIsInZhbHVlIjoiNmpVNTVuSzBXeDZOZjFqZWN5K05VZnY4Y2c2YmFWYzU0NFVVcDVhcmtNVWFDWVhzUDAxekd6WTNFQ0tTU3NNQ0d5WWlJZzBXc1g5R1NMeFhLK0xvRFg5NW0rQmJncDdEaUl0c2NETlkxR05pMVEzWGNFTmwwUCtJeFQrbjRtSmgiLCJtYWMiOiI4YTZiZDQ2MGY5MDZjZDIwZjkxMzU3ZTQ3NzY2NjQ3MDVhNjE0MDg4ZmYxNTY4YzEyNGFhZTBmMTJhNjhmMzk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904725503\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1980628488 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MZLk9kQQ1tgzc6FQKZD7mzszobNECcH1RbGnygmy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980628488\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1517945830 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 09:42:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517945830\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1640140603 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3chO4zB7YMwxUaUijGPRGbO35yauacROVu5ZNiuk</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/wallet/add-funds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">01991e3e-2ac7-73a5-b86e-ba67f54988d5</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640140603\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/analytics", "action_name": "user.analytics", "controller_action": "App\\Http\\Controllers\\UserController@analytics"}, "badge": null}}