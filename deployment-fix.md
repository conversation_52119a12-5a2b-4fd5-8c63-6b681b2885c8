# Laravel Cloud Deployment Fix for UUID Error

## Problem

Getting 500 Server Error with this log entry:

```
ERROR: SQLSTATE[22P02]: Invalid text representation: 7 ERROR: invalid input syntax for type uuid: "11"
```

## Root Cause

The application is trying to find users using integer IDs instead of UUIDs, likely due to cached session data or configuration mismatch.

## Solution

### Step 1: Clear All Caches and Sessions on Laravel Cloud

Run these commands in your Laravel Cloud deployment terminal:

```bash
# Clear all Laravel caches and sessions aggressively
php artisan debug:user-query

# Alternative manual commands if the above doesn't work:
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Clear our custom invalid sessions
php artisan sessions:clear-invalid-uuids
```

### Step 2: Regenerate Configuration

```bash
# Regenerate config cache with correct environment
php artisan config:cache
```

### Step 3: Verify Database Connection

```bash
# Test database connection and verify UUID format
php artisan tinker
```

In tinker, run:

```php
\App\Models\User::first(); // Should return a user with UUID
exit
```

### Step 4: Force Fresh Authentication State

If the above doesn't work, you may need to:

```bash
# Truncate sessions table (only if safe to logout all users)
php artisan db:wipe --drop-views
php artisan migrate --force
php artisan db:seed --force
```

## Prevention

The new middleware `ValidateUuidParameters` will prevent future occurrences of this issue by validating UUID format in routes.

## Quick Test

After applying the fix, test by visiting:

- `/membership` (the page that was causing errors)
- Any admin user management pages
- Authentication flows

The error should be resolved and your application should work normally.
