import { Card, CardContent } from '@/components/ui/card';
import { DollarSign, FolderOpen, Target, Users } from 'lucide-react';

interface UserStats {
    total_users: number;
    new_users_this_month: number;
    active_users: number;
    verified_users: number;
    users_by_role: Record<string, number>;
}

interface ProjectStats {
    total_projects: number;
    open_projects: number;
    in_progress_projects: number;
    completed_projects: number;
    projects_by_category: Record<string, number>;
    projects_by_academic_level: Record<string, number>;
}

interface FinancialStats {
    total_wallet_balance: number;
    total_transactions: number;
    total_deposits: number;
    total_withdrawals: number;
    pending_withdrawals: number;
    total_commission_earned?: number;
    pending_commission?: number;
    commission_withdrawn?: number;
    available_commission?: number;
    revenue_by_month: Array<{
        month: string;
        deposits: number;
        withdrawals: number;
    }>;
    commission_by_month?: Array<{
        month: string;
        total_commission: number;
    }>;
}

interface BidStats {
    total_bids: number;
    accepted_bids: number;
    pending_bids: number;
    rejected_bids: number;
    average_bid_amount?: number;
}

interface MetricsCardsProps {
    userStats: UserStats;
    projectStats: ProjectStats;
    financialStats: FinancialStats;
    bidStats: BidStats;
    permissions: {
        can_view_financial_data: boolean;
    };
    formatCurrency: (amount: number) => string;
}

export default function MetricsCards({ userStats, projectStats, financialStats, bidStats, permissions, formatCurrency }: MetricsCardsProps) {
    return (
        <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Total Users</p>
                            <p className="text-3xl font-bold text-blue-600">{userStats.total_users}</p>
                            <p className="text-xs text-green-600">+{userStats.new_users_this_month} this month</p>
                        </div>
                        <Users className="h-12 w-12 text-blue-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Total Projects</p>
                            <p className="text-3xl font-bold text-green-600">{projectStats.total_projects}</p>
                            <p className="text-xs text-gray-500">{projectStats.open_projects} open</p>
                        </div>
                        <FolderOpen className="h-12 w-12 text-green-600" />
                    </div>
                </CardContent>
            </Card>

            {/* Financial Stats - Only for super admins */}
            {permissions.can_view_financial_data && (
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total Wallet Balance</p>
                                <p className="text-3xl font-bold text-purple-600">{formatCurrency(financialStats.total_wallet_balance)}</p>
                                <p className="text-xs text-gray-500">{financialStats.total_transactions} transactions</p>
                            </div>
                            <DollarSign className="h-12 w-12 text-purple-600" />
                        </div>
                    </CardContent>
                </Card>
            )}

            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-600">Total Bids</p>
                            <p className="text-3xl font-bold text-orange-600">{bidStats.total_bids}</p>
                            <p className="text-xs text-gray-500">{bidStats.accepted_bids} accepted</p>
                        </div>
                        <Target className="h-12 w-12 text-orange-600" />
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
