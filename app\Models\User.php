<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

/**
 * @property string $id
 * @property string $name
 * @property string $email
 * @property string|null $email_verified_at
 * @property string $password
 * @property string|null $remember_token
 * @property string|null $google_id
 * @property string $role
 * @property string $status
 * @property float $wallet_balance
 * @property string|null $bio
 * @property array|null $skills
 * @property string|null $education
 * @property bool $is_verified
 * @property string|null $phone
 * @property string|null $location
 * @property \Carbon\Carbon|null $last_activity
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, HasUuids, Notifiable;

    /**
     * The data type of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'role',
        'status',
        'wallet_balance',
        'bio',
        'skills',
        'education',
        'is_verified',
        'phone',
        'location',
        'last_activity',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'skills' => 'array',
            'last_activity' => 'datetime',
            'wallet_balance' => 'decimal:2',
        ];
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    /**
     * Check if user can access admin features.
     */
    public function canAccessAdmin(): bool
    {
        return in_array($this->role, ['admin', 'super_admin']);
    }

    /**
     * Projects posted by this user (as client).
     */
    public function projects()
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Bids made by this user (as freelancer).
     */
    public function bids()
    {
        return $this->hasMany(Bid::class);
    }

    /**
     * Wallet transactions for this user.
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Ratings given by this user.
     */
    public function givenRatings()
    {
        return $this->hasMany(Rating::class, 'client_id')->orWhere('freelancer_id', $this->id);
    }

    /**
     * Ratings received by this user.
     */
    public function receivedRatings()
    {
        return $this->hasMany(Rating::class, 'freelancer_id')->orWhere('client_id', $this->id);
    }

    /**
     * Calculate average rating for this user.
     */
    public function averageRating(): float
    {
        return $this->receivedRatings()->avg('rating') ?: 0;
    }

    /**
     * Get total rating count.
     */
    public function totalRatings(): int
    {
        return $this->receivedRatings()->count();
    }

    /**
     * Resolve the route binding with UUID validation.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        // Ensure the value is a valid UUID format
        if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $value)) {
            return null; // Return null to trigger 404
        }

        return parent::resolveRouteBinding($value, $field);
    }
}
