import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, DollarSign, Edit, Shield, Star, Trash2, User, Wallet, XCircle } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number;
    phone?: string;
    location?: string;
    bio?: string;
    skills?: string[];
    education?: string;
    created_at: string;
    projects: any[];
    bids: any[];
    wallet_transactions: any[];
}

interface UserStats {
    total_projects: number;
    total_bids: number;
    total_spent: number;
    total_earned: number;
    average_rating: number;
    total_ratings: number;
}

interface Props {
    user: User;
    user_stats: UserStats;
}

export default function UserDetails({ user, user_stats }: Props) {
    const { auth } = usePage<{ auth: { user: User } }>().props;
    const [showEditDialog, setShowEditDialog] = useState(false);
    const [showStatusDialog, setShowStatusDialog] = useState(false);
    const [showWalletDialog, setShowWalletDialog] = useState(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);

    const {
        data: editData,
        setData: setEditData,
        put,
        processing: editProcessing,
        errors: editErrors,
    } = useForm({
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        is_verified: user.is_verified,
        bio: user.bio || '',
        phone: user.phone || '',
        location: user.location || '',
        skills: user.skills || [],
        education: user.education || '',
    });

    const {
        data: statusData,
        setData: setStatusData,
        patch: patchStatus,
        processing: statusProcessing,
        errors: statusErrors,
    } = useForm({
        status: user.status,
        reason: '',
    });

    const {
        data: walletData,
        setData: setWalletData,
        post: postWallet,
        processing: walletProcessing,
        errors: walletErrors,
    } = useForm({
        amount: '',
        type: 'subtract' as 'subtract',
        reason: '',
    });

    const { delete: deleteUser, processing: deleteProcessing } = useForm();

    const handleEdit = () => {
        put(route('admin.users.update', user.id), {
            onSuccess: () => setShowEditDialog(false),
        });
    };

    const handleStatusUpdate = () => {
        patchStatus(route('admin.users.update-status', user.id), {
            onSuccess: () => setShowStatusDialog(false),
        });
    };

    const handleWalletAdjustment = () => {
        postWallet(route('admin.users.adjust-wallet', user.id), {
            onSuccess: () => {
                setShowWalletDialog(false);
                setWalletData({ amount: '', type: 'subtract', reason: '' });
            },
        });
    };

    const handleDelete = () => {
        deleteUser(route('admin.users.destroy', user.id), {
            onSuccess: () => router.visit(route('admin.users.index')),
        });
    };

    const toggleVerification = () => {
        router.patch(route('admin.users.update-verification', user.id), {
            is_verified: !user.is_verified,
        });
    };

    const getRoleColor = (role: string) => {
        switch (role) {
            case 'super_admin':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            case 'admin':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'suspended':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            case 'blocked':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
        }
    };

    return (
        <AppLayout>
            <Head title={`User Details - ${user.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href={route('admin.users.index')}
                            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
                        >
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Users
                        </Link>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            onClick={() => setShowEditDialog(true)}
                            className="border-primary text-primary hover:bg-primary hover:text-primary-foreground"
                        >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => setShowStatusDialog(true)}
                            className="border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground"
                        >
                            <Shield className="mr-2 h-4 w-4" />
                            Update Status
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => setShowWalletDialog(true)}
                            className="border-accent text-accent hover:bg-accent hover:text-accent-foreground"
                        >
                            <Wallet className="mr-2 h-4 w-4" />
                            Adjust Wallet
                        </Button>
                        {auth.user.role === 'super_admin' && user.role !== 'super_admin' && (
                            <Button variant="destructive" onClick={() => setShowDeleteDialog(true)}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete User
                            </Button>
                        )}
                    </div>
                </div>

                {/* User Information */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    <div className="space-y-6 lg:col-span-2">
                        {/* Basic Info */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <User className="h-5 w-5" />
                                    <span>User Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Name</label>
                                        <p className="text-sm font-medium">{user.name}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Email</label>
                                        <p className="text-sm">{user.email}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Phone</label>
                                        <p className="text-sm">{user.phone || 'Not provided'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Location</label>
                                        <p className="text-sm">{user.location || 'Not provided'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Education</label>
                                        <p className="text-sm">{user.education || 'Not provided'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Member Since</label>
                                        <p className="text-sm">{new Date(user.created_at).toLocaleDateString()}</p>
                                    </div>
                                </div>
                                {user.bio && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Bio</label>
                                        <p className="mt-1 text-sm">{user.bio}</p>
                                    </div>
                                )}
                                {user.skills && user.skills.length > 0 && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Skills</label>
                                        <div className="mt-1 flex flex-wrap gap-2">
                                            {user.skills.map((skill, index) => (
                                                <Badge key={index} variant="secondary">
                                                    {skill}
                                                </Badge>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Recent Transactions */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Wallet className="h-5 w-5" />
                                    <span>Recent Transactions</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {user.wallet_transactions.length > 0 ? (
                                    <div className="space-y-2">
                                        {user.wallet_transactions.map((transaction, index) => (
                                            <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                                                <div>
                                                    <p className="text-sm font-medium">{transaction.description}</p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {new Date(transaction.created_at).toLocaleDateString()}
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p
                                                        className={`text-sm font-medium ${
                                                            transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                                                        }`}
                                                    >
                                                        {transaction.type === 'deposit' ? '+' : '-'}${transaction.amount}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">Balance: ${transaction.balance_after}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="text-muted-foreground">No transactions found.</p>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    <div className="space-y-6">
                        {/* Status & Role */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Status & Permissions</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Role</span>
                                    <Badge className={getRoleColor(user.role)}>{user.role.replace('_', ' ').toUpperCase()}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Status</span>
                                    <Badge className={getStatusColor(user.status)}>{user.status.toUpperCase()}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Verified</span>
                                    <Button variant="ghost" size="sm" onClick={toggleVerification} className="h-auto p-0">
                                        {user.is_verified ? (
                                            <CheckCircle className="h-5 w-5 text-green-600" />
                                        ) : (
                                            <XCircle className="h-5 w-5 text-red-600" />
                                        )}
                                    </Button>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Wallet Balance</span>
                                    <span className="text-sm font-bold">${user.wallet_balance}</span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Stats */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Statistics</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="rounded-lg bg-muted p-3 text-center">
                                        <p className="text-2xl font-bold">{user_stats.total_projects}</p>
                                        <p className="text-xs text-muted-foreground">Projects</p>
                                    </div>
                                    <div className="rounded-lg bg-muted p-3 text-center">
                                        <p className="text-2xl font-bold">{user_stats.total_bids}</p>
                                        <p className="text-xs text-muted-foreground">Bids</p>
                                    </div>
                                    <div className="rounded-lg bg-muted p-3 text-center">
                                        <p className="text-2xl font-bold">${user_stats.total_spent}</p>
                                        <p className="text-xs text-muted-foreground">Spent</p>
                                    </div>
                                    <div className="rounded-lg bg-muted p-3 text-center">
                                        <p className="text-2xl font-bold">${user_stats.total_earned}</p>
                                        <p className="text-xs text-muted-foreground">Earned</p>
                                    </div>
                                </div>
                                {user_stats.total_ratings > 0 && (
                                    <div className="rounded-lg bg-muted p-3 text-center">
                                        <div className="flex items-center justify-center space-x-1">
                                            <Star className="h-4 w-4 fill-current text-yellow-500" />
                                            <span className="text-lg font-bold">{user_stats.average_rating.toFixed(1)}</span>
                                        </div>
                                        <p className="text-xs text-muted-foreground">
                                            {user_stats.total_ratings} rating{user_stats.total_ratings !== 1 ? 's' : ''}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Edit User Dialog */}
                <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Edit User</DialogTitle>
                            <DialogDescription>Update user information and permissions.</DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="name">Name</Label>
                                    <Input id="name" value={editData.name} onChange={(e) => setEditData('name', e.target.value)} />
                                    {editErrors.name && <p className="text-sm text-destructive">{editErrors.name}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="email">Email</Label>
                                    <Input id="email" type="email" value={editData.email} onChange={(e) => setEditData('email', e.target.value)} />
                                    {editErrors.email && <p className="text-sm text-destructive">{editErrors.email}</p>}
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="role">Role</Label>
                                    <Select
                                        value={editData.role}
                                        onValueChange={(value: 'user' | 'admin' | 'super_admin') => setEditData('role', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="user">User</SelectItem>
                                            <SelectItem value="admin">Admin</SelectItem>
                                            <SelectItem value="super_admin">Super Admin</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {editErrors.role && <p className="text-sm text-destructive">{editErrors.role}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={editData.status}
                                        onValueChange={(value: 'active' | 'suspended' | 'blocked') => setEditData('status', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="suspended">Suspended</SelectItem>
                                            <SelectItem value="blocked">Blocked</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {editErrors.status && <p className="text-sm text-destructive">{editErrors.status}</p>}
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="phone">Phone</Label>
                                    <Input id="phone" value={editData.phone} onChange={(e) => setEditData('phone', e.target.value)} />
                                    {editErrors.phone && <p className="text-sm text-destructive">{editErrors.phone}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="location">Location</Label>
                                    <Input id="location" value={editData.location} onChange={(e) => setEditData('location', e.target.value)} />
                                    {editErrors.location && <p className="text-sm text-destructive">{editErrors.location}</p>}
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="education">Education</Label>
                                <Input id="education" value={editData.education} onChange={(e) => setEditData('education', e.target.value)} />
                                {editErrors.education && <p className="text-sm text-destructive">{editErrors.education}</p>}
                            </div>
                            <div>
                                <Label htmlFor="bio">Bio</Label>
                                <Textarea id="bio" value={editData.bio} onChange={(e) => setEditData('bio', e.target.value)} rows={3} />
                                {editErrors.bio && <p className="text-sm text-destructive">{editErrors.bio}</p>}
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleEdit} disabled={editProcessing}>
                                {editProcessing ? 'Saving...' : 'Save Changes'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Status Update Dialog */}
                <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Update User Status</DialogTitle>
                            <DialogDescription>Change the user's account status.</DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div>
                                <Label htmlFor="status">Status</Label>
                                <Select
                                    value={statusData.status}
                                    onValueChange={(value: 'active' | 'suspended' | 'blocked') => setStatusData('status', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="suspended">Suspended</SelectItem>
                                        <SelectItem value="blocked">Blocked</SelectItem>
                                    </SelectContent>
                                </Select>
                                {statusErrors.status && <p className="text-sm text-destructive">{statusErrors.status}</p>}
                            </div>
                            <div>
                                <Label htmlFor="reason">Reason (Optional)</Label>
                                <Textarea
                                    id="reason"
                                    value={statusData.reason}
                                    onChange={(e) => setStatusData('reason', e.target.value)}
                                    rows={3}
                                    placeholder="Reason for status change..."
                                />
                                {statusErrors.reason && <p className="text-sm text-destructive">{statusErrors.reason}</p>}
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowStatusDialog(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleStatusUpdate} disabled={statusProcessing}>
                                {statusProcessing ? 'Updating...' : 'Update Status'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Wallet Adjustment Dialog */}
                <Dialog open={showWalletDialog} onOpenChange={setShowWalletDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Adjust Wallet Balance</DialogTitle>
                            <DialogDescription>Add or subtract funds from the user's wallet.</DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div>
                                <Label htmlFor="type">Action</Label>
                                <Select value={walletData.type} onValueChange={(value: 'subtract') => setWalletData('type', value)}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="subtract">Subtract Funds</SelectItem>
                                    </SelectContent>
                                </Select>
                                {walletErrors.type && <p className="text-sm text-destructive">{walletErrors.type}</p>}
                            </div>
                            <div>
                                <Label htmlFor="amount">Amount</Label>
                                <Input
                                    id="amount"
                                    type="number"
                                    step="0.01"
                                    value={walletData.amount}
                                    onChange={(e) => setWalletData('amount', e.target.value)}
                                    placeholder="0.00"
                                />
                                {walletErrors.amount && <p className="text-sm text-destructive">{walletErrors.amount}</p>}
                            </div>
                            <div>
                                <Label htmlFor="wallet-reason">Reason</Label>
                                <Textarea
                                    id="wallet-reason"
                                    value={walletData.reason}
                                    onChange={(e) => setWalletData('reason', e.target.value)}
                                    rows={3}
                                    placeholder="Reason for wallet adjustment..."
                                />
                                {walletErrors.reason && <p className="text-sm text-destructive">{walletErrors.reason}</p>}
                            </div>
                            <Alert>
                                <DollarSign className="h-4 w-4" />
                                <AlertDescription>Current balance: ${user.wallet_balance}</AlertDescription>
                            </Alert>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowWalletDialog(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleWalletAdjustment} disabled={walletProcessing}>
                                {walletProcessing ? 'Processing...' : 'Adjust Balance'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Delete Confirmation Dialog */}
                <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Delete User</DialogTitle>
                            <DialogDescription>Are you sure you want to delete this user? This action cannot be undone.</DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                                Cancel
                            </Button>
                            <Button variant="destructive" onClick={handleDelete} disabled={deleteProcessing}>
                                {deleteProcessing ? 'Deleting...' : 'Delete User'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
