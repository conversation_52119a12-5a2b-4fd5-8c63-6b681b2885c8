import EditDialogs from '@/components/Admin/UserDetails/EditDialogs';
import TransactionHistory from '@/components/Admin/UserDetails/TransactionHistory';
import UserInfo from '@/components/Admin/UserDetails/UserInfo';
import UserStatusCard from '@/components/Admin/UserDetails/UserStatusCard';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { ArrowLeft, Edit, Shield, Trash2, Wallet } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: string;
    name: string;
    email: string;
    role: 'user' | 'admin' | 'super_admin';
    status: 'active' | 'suspended' | 'blocked';
    is_verified: boolean;
    wallet_balance: number;
    phone?: string;
    location?: string;
    bio?: string;
    skills?: string[];
    education?: string;
    created_at: string;
    projects: any[];
    bids: any[];
    wallet_transactions: any[];
}

interface UserStats {
    total_projects: number;
    total_bids: number;
    total_spent: number;
    total_earned: number;
    average_rating: number;
    total_ratings: number;
}

interface Props {
    user: User;
    user_stats: UserStats;
}

export default function UserDetails({ user, user_stats }: Props) {
    const { auth } = usePage<{ auth: { user: User } }>().props;
    const [showEditDialog, setShowEditDialog] = useState(false);
    const [showStatusDialog, setShowStatusDialog] = useState(false);
    const [showWalletDialog, setShowWalletDialog] = useState(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);

    const {
        data: editData,
        setData: setEditData,
        put,
        processing,
        errors,
    } = useForm({
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        is_verified: user.is_verified,
        bio: user.bio || '',
        phone: user.phone || '',
        location: user.location || '',
        skills: user.skills || [],
        education: user.education || '',
    });

    const {
        data: statusData,
        setData: setStatusData,
        patch: patchStatus,
        processing: statusProcessing,
        errors: statusErrors,
    } = useForm({
        status: user.status,
        reason: '',
    });

    const {
        data: walletData,
        setData: setWalletData,
        post: postWallet,
        processing: walletProcessing,
        errors: walletErrors,
    } = useForm({
        amount: '',
        type: 'subtract' as 'subtract',
        reason: '',
    });

    const { delete: deleteUser, processing: deleteProcessing } = useForm();

    const handleEdit = () => {
        put(route('admin.users.update', user.id), {
            onSuccess: () => setShowEditDialog(false),
        });
    };

    const handleStatusUpdate = () => {
        patchStatus(route('admin.users.update-status', user.id), {
            onSuccess: () => setShowStatusDialog(false),
        });
    };

    const handleWalletAdjustment = () => {
        postWallet(route('admin.users.adjust-wallet', user.id), {
            onSuccess: () => {
                setShowWalletDialog(false);
                setWalletData({ amount: '', type: 'subtract', reason: '' });
            },
        });
    };

    const handleDelete = () => {
        deleteUser(route('admin.users.destroy', user.id), {
            onSuccess: () => router.visit(route('admin.users.index')),
        });
    };

    const toggleVerification = () => {
        router.patch(route('admin.users.update-verification', user.id), {
            is_verified: !user.is_verified,
        });
    };

    return (
        <AppLayout>
            <Head title={`User Details - ${user.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href={route('admin.users.index')}
                            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
                        >
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Users
                        </Link>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            onClick={() => setShowEditDialog(true)}
                            className="border-primary text-primary hover:bg-primary hover:text-primary-foreground"
                        >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => setShowStatusDialog(true)}
                            className="border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground"
                        >
                            <Shield className="mr-2 h-4 w-4" />
                            Update Status
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => setShowWalletDialog(true)}
                            className="border-accent text-accent hover:bg-accent hover:text-accent-foreground"
                        >
                            <Wallet className="mr-2 h-4 w-4" />
                            Adjust Wallet
                        </Button>
                        {auth.user.role === 'super_admin' && user.role !== 'super_admin' && (
                            <Button variant="destructive" onClick={() => setShowDeleteDialog(true)}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete User
                            </Button>
                        )}
                    </div>
                </div>

                {/* User Information */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    <div className="space-y-6 lg:col-span-2">
                        <UserInfo user={user} user_stats={user_stats} />
                        <TransactionHistory wallet_transactions={user.wallet_transactions} />
                    </div>
                    <div className="space-y-6">
                        <UserStatusCard user={user} user_stats={user_stats} onToggleVerification={toggleVerification} />
                    </div>
                </div>

                {/* Edit Dialogs */}
                <EditDialogs
                    user={user}
                    showEditDialog={showEditDialog}
                    showStatusDialog={showStatusDialog}
                    showWalletDialog={showWalletDialog}
                    showDeleteDialog={showDeleteDialog}
                    editData={editData}
                    statusData={statusData}
                    walletData={walletData}
                    editErrors={errors}
                    statusErrors={statusErrors}
                    walletErrors={walletErrors}
                    editProcessing={processing}
                    statusProcessing={statusProcessing}
                    walletProcessing={walletProcessing}
                    deleteProcessing={deleteProcessing}
                    setEditData={setEditData}
                    setStatusData={setStatusData}
                    setWalletData={setWalletData}
                    onEditClose={() => setShowEditDialog(false)}
                    onStatusClose={() => setShowStatusDialog(false)}
                    onWalletClose={() => setShowWalletDialog(false)}
                    onDeleteClose={() => setShowDeleteDialog(false)}
                    onEdit={handleEdit}
                    onStatusUpdate={handleStatusUpdate}
                    onWalletAdjustment={handleWalletAdjustment}
                    onDelete={handleDelete}
                />
            </div>
        </AppLayout>
    );
}
