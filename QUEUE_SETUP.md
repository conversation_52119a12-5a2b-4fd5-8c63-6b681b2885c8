# Notification System Setup

## Queue Worker Setup

The notification system requires a queue worker to be running to process notifications in real-time.

### Start Queue Worker (Development)

```bash
php artisan queue:work
```

### Start Queue Worker (Production - Background)

```bash
# Run as daemon
php artisan queue:work --daemon

# With timeout and sleep settings (recommended for production)
php artisan queue:work --daemon --timeout=60 --sleep=3 --tries=3
```

### Supervisor Configuration (Recommended for Production)

Create a supervisor configuration file at `/etc/supervisor/conf.d/laravel-worker.conf`:

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/project/artisan queue:work database --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasneeded=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/worker.log
stopwaitsecs=3600
```

### Windows Service (For Windows Servers)

Use tools like NSSM (Non-Sucking Service Manager) to run the queue worker as a Windows service.

## Troubleshooting

### Check Queue Status

```bash
# Check failed jobs
php artisan queue:failed

# Check jobs table
php artisan queue:monitor database

# Clear failed jobs
php artisan queue:flush
```

### Restart Queue Workers

```bash
# Gracefully restart workers
php artisan queue:restart
```

### Manual Processing (Testing Only)

```bash
# Process one job
php artisan queue:work --once

# Process all jobs and stop
php artisan queue:work --stop-when-empty
```

## Notification Testing

### Test Notification Creation

```php
// In tinker: php artisan tinker
$user = User::first();
$user->notify(new \App\Notifications\BidSubmittedNotification($bid, $project, $freelancer));
```

### Check Notification Count

```php
// In tinker
$user = User::find(1);
$user->unreadNotifications()->count(); // Get unread count
$user->notifications()->count(); // Get total count
```
