{"__meta": {"id": "01K4G54RDP50NKFDSV9SKV6J26", "datetime": "2025-09-06 18:38:13", "utime": **********.944051, "method": "GET", "uri": "/admin/analytics", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.253351, "end": **********.94407, "duration": 0.6907191276550293, "duration_str": "691ms", "measures": [{"label": "Booting", "start": **********.253351, "relative_start": 0, "end": **********.42935, "relative_end": **********.42935, "duration": 0.*****************, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.429363, "relative_start": 0.****************, "end": **********.944073, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "515ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.458805, "relative_start": 0.*****************, "end": **********.462495, "relative_end": **********.462495, "duration": 0.003690004348754883, "duration_str": "3.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.919561, "relative_start": 0.****************, "end": **********.941434, "relative_end": **********.941434, "duration": 0.021872997283935547, "duration_str": "21.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Admin/Analytics", "param_count": null, "params": [], "start": **********.944021, "type": "", "hash": "Admin/Analytics"}]}, "queries": {"count": 44, "nb_statements": 43, "nb_visible_statements": 44, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.21608999999999998, "accumulated_duration_str": "216ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.480361, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'C2p0k1IM1rZwitYlztIXeh1yN0zp0XPtgAcmeUgp' limit 1", "type": "query", "params": [], "bindings": ["C2p0k1IM1rZwitYlztIXeh1yN0zp0XPtgAcmeUgp"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.486293, "duration": 0.06383, "duration_str": "63.83ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 29.539}, {"sql": "select * from \"users\" where \"id\" = '********-e360-7219-aadc-1c26c87b37c1' limit 1", "type": "query", "params": [], "bindings": ["********-e360-7219-aadc-1c26c87b37c1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.5604239, "duration": 0.00724, "duration_str": "7.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 29.539, "width_percent": 3.35}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.575964, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:32", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=32", "ajax": false, "filename": "AnalyticsController.php", "line": "32"}, "connection": "thesylink", "explain": null, "start_percent": 32.889, "width_percent": 1.694}, {"sql": "select count(*) as aggregate from \"users\" where extract(month from \"created_at\") = '09'", "type": "query", "params": [], "bindings": ["09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.5815601, "duration": 0.00873, "duration_str": "8.73ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:33", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=33", "ajax": false, "filename": "AnalyticsController.php", "line": "33"}, "connection": "thesylink", "explain": null, "start_percent": 34.583, "width_percent": 4.04}, {"sql": "select count(*) as aggregate from \"users\" where \"status\" = 'active'", "type": "query", "params": [], "bindings": ["active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.592294, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:34", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=34", "ajax": false, "filename": "AnalyticsController.php", "line": "34"}, "connection": "thesylink", "explain": null, "start_percent": 38.623, "width_percent": 1.49}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.597417, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:35", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=35", "ajax": false, "filename": "AnalyticsController.php", "line": "35"}, "connection": "thesylink", "explain": null, "start_percent": 40.113, "width_percent": 1.448}, {"sql": "select \"role\", count(*) as count from \"users\" group by \"role\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.643291, "duration": 0.01055, "duration_str": "10.55ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:38", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=38", "ajax": false, "filename": "AnalyticsController.php", "line": "38"}, "connection": "thesylink", "explain": null, "start_percent": 41.561, "width_percent": 4.882}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.6565292, "duration": 0.0052, "duration_str": "5.2ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:44", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=44", "ajax": false, "filename": "AnalyticsController.php", "line": "44"}, "connection": "thesylink", "explain": null, "start_percent": 46.444, "width_percent": 2.406}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'open'", "type": "query", "params": [], "bindings": ["open"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.663246, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=45", "ajax": false, "filename": "AnalyticsController.php", "line": "45"}, "connection": "thesylink", "explain": null, "start_percent": 48.85, "width_percent": 1.573}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'in_progress'", "type": "query", "params": [], "bindings": ["in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.668464, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:46", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=46", "ajax": false, "filename": "AnalyticsController.php", "line": "46"}, "connection": "thesylink", "explain": null, "start_percent": 50.423, "width_percent": 2.545}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.675193, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=47", "ajax": false, "filename": "AnalyticsController.php", "line": "47"}, "connection": "thesylink", "explain": null, "start_percent": 52.969, "width_percent": 1.134}, {"sql": "select \"category\", count(*) as count from \"projects\" where \"category\" is not null group by \"category\" order by \"count\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 53}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.679184, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:53", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=53", "ajax": false, "filename": "AnalyticsController.php", "line": "53"}, "connection": "thesylink", "explain": null, "start_percent": 54.102, "width_percent": 1.684}, {"sql": "select \"academic_level\", count(*) as count from \"projects\" where \"academic_level\" is not null group by \"academic_level\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 58}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.683937, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:58", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=58", "ajax": false, "filename": "AnalyticsController.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 55.787, "width_percent": 1.018}, {"sql": "select sum(\"wallet_balance\") as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.687276, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:66", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=66", "ajax": false, "filename": "AnalyticsController.php", "line": "66"}, "connection": "thesylink", "explain": null, "start_percent": 56.805, "width_percent": 1.175}, {"sql": "select count(*) as aggregate from \"wallet_transactions\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.69245, "duration": 0.00634, "duration_str": "6.34ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:67", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=67", "ajax": false, "filename": "AnalyticsController.php", "line": "67"}, "connection": "thesylink", "explain": null, "start_percent": 57.98, "width_percent": 2.934}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'deposit' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["deposit", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.700099, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=68", "ajax": false, "filename": "AnalyticsController.php", "line": "68"}, "connection": "thesylink", "explain": null, "start_percent": 60.914, "width_percent": 1.259}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["withdrawal", "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.704197, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:69", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=69", "ajax": false, "filename": "AnalyticsController.php", "line": "69"}, "connection": "thesylink", "explain": null, "start_percent": 62.173, "width_percent": 1.074}, {"sql": "select sum(\"amount\") as aggregate from \"wallet_transactions\" where \"type\" = 'withdrawal' and \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["withdrawal", "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 70}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7076921, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:70", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=70", "ajax": false, "filename": "AnalyticsController.php", "line": "70"}, "connection": "thesylink", "explain": null, "start_percent": 63.247, "width_percent": 1.18}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"status\" = 'collected'", "type": "query", "params": [], "bindings": ["collected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 73}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7127619, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:73", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=73", "ajax": false, "filename": "AnalyticsController.php", "line": "73"}, "connection": "thesylink", "explain": null, "start_percent": 64.427, "width_percent": 2.036}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.718557, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:74", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=74", "ajax": false, "filename": "AnalyticsController.php", "line": "74"}, "connection": "thesylink", "explain": null, "start_percent": 66.463, "width_percent": 0.93}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"withdrawn_at\" is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.721884, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:75", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=75", "ajax": false, "filename": "AnalyticsController.php", "line": "75"}, "connection": "thesylink", "explain": null, "start_percent": 67.393, "width_percent": 0.944}, {"sql": "select sum(\"amount\") as aggregate from \"platform_commissions\" where \"status\" = 'collected' and \"withdrawn_at\" is null", "type": "query", "params": [], "bindings": ["collected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7251852, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:76", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=76", "ajax": false, "filename": "AnalyticsController.php", "line": "76"}, "connection": "thesylink", "explain": null, "start_percent": 68.337, "width_percent": 0.935}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END) as deposits, SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END) as withdrawals from \"wallet_transactions\" where extract(year from \"created_at\") = 2025 group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": [2025], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7284338, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:86", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=86", "ajax": false, "filename": "AnalyticsController.php", "line": "86"}, "connection": "thesylink", "explain": null, "start_percent": 69.272, "width_percent": 1.175}, {"sql": "select TO_CHAR(collected_at, 'YYYY-MM') as month, SUM(amount) as total_commission from \"platform_commissions\" where \"status\" = 'collected' and extract(year from \"collected_at\") = 2025 group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["collected", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.732152, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:97", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=97", "ajax": false, "filename": "AnalyticsController.php", "line": "97"}, "connection": "thesylink", "explain": null, "start_percent": 70.447, "width_percent": 0.944}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 104}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7363482, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:104", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=104", "ajax": false, "filename": "AnalyticsController.php", "line": "104"}, "connection": "thesylink", "explain": null, "start_percent": 71.392, "width_percent": 1.925}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.7415679, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:105", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=105", "ajax": false, "filename": "AnalyticsController.php", "line": "105"}, "connection": "thesylink", "explain": null, "start_percent": 73.317, "width_percent": 0.847}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.744416, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=106", "ajax": false, "filename": "AnalyticsController.php", "line": "106"}, "connection": "thesylink", "explain": null, "start_percent": 74.164, "width_percent": 0.764}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'rejected'", "type": "query", "params": [], "bindings": ["rejected"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.746942, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:107", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=107", "ajax": false, "filename": "AnalyticsController.php", "line": "107"}, "connection": "thesylink", "explain": null, "start_percent": 74.927, "width_percent": 0.791}, {"sql": "select avg(\"amount\") as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 112}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.749979, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:112", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=112", "ajax": false, "filename": "AnalyticsController.php", "line": "112"}, "connection": "thesylink", "explain": null, "start_percent": 75.718, "width_percent": 1.55}, {"sql": "select \"id\", \"name\", \"email\", \"created_at\", \"role\" from \"users\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.754622, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:117", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=117", "ajax": false, "filename": "AnalyticsController.php", "line": "117"}, "connection": "thesylink", "explain": null, "start_percent": 77.269, "width_percent": 1.055}, {"sql": "select \"id\", \"title\", \"user_id\", \"status\", \"created_at\" from \"projects\" order by \"created_at\" desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.758186, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:118", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=118", "ajax": false, "filename": "AnalyticsController.php", "line": "118"}, "connection": "thesylink", "explain": null, "start_percent": 78.324, "width_percent": 1.037}, {"sql": "select \"id\", \"name\" from \"users\" where \"users\".\"id\" in ('********-e98c-734a-8386-766ce2fd8656', '********-eaba-72bb-8cc4-a660695f2af1', '********-ebf1-7009-95a5-d535c8fcddb2', '********-ed30-7302-b20d-54cd8088aee3', '********-ee6f-7187-9b5c-504b00733477')", "type": "query", "params": [], "bindings": ["********-e98c-734a-8386-766ce2fd8656", "********-eaba-72bb-8cc4-a660695f2af1", "********-ebf1-7009-95a5-d535c8fcddb2", "********-ed30-7302-b20d-54cd8088aee3", "********-ee6f-7187-9b5c-504b00733477"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.8543808, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:118", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=118", "ajax": false, "filename": "AnalyticsController.php", "line": "118"}, "connection": "thesylink", "explain": null, "start_percent": 79.36, "width_percent": 1.907}, {"sql": "select \"id\", \"user_id\", \"type\", \"amount\", \"status\", \"created_at\" from \"wallet_transactions\" order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 126}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.859812, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:126", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=126", "ajax": false, "filename": "AnalyticsController.php", "line": "126"}, "connection": "thesylink", "explain": null, "start_percent": 81.267, "width_percent": 1.263}, {"sql": "select \"id\", \"project_id\", \"milestone_id\", \"amount\", \"status\", \"collected_at\" from \"platform_commissions\" order by \"created_at\" desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.8636668, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:131", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=131", "ajax": false, "filename": "AnalyticsController.php", "line": "131"}, "connection": "thesylink", "explain": null, "start_percent": 82.53, "width_percent": 1.259}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"users\" where \"created_at\" >= '2024-09-06 18:38:13' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 18:38:13"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 143}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.868149, "duration": 0.011869999999999999, "duration_str": "11.87ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:143", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=143", "ajax": false, "filename": "AnalyticsController.php", "line": "143"}, "connection": "thesylink", "explain": null, "start_percent": 83.789, "width_percent": 5.493}, {"sql": "select TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count from \"projects\" where \"created_at\" >= '2024-09-06 18:38:13' group by \"month\" order by \"month\" asc", "type": "query", "params": [], "bindings": ["2024-09-06 18:38:13"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 152}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.8846211, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:152", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=152", "ajax": false, "filename": "AnalyticsController.php", "line": "152"}, "connection": "thesylink", "explain": null, "start_percent": 89.282, "width_percent": 1.203}, {"sql": "select count(*) as aggregate from \"projects\" where \"status\" = 'completed'", "type": "query", "params": [], "bindings": ["completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.888612, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:158", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=158", "ajax": false, "filename": "AnalyticsController.php", "line": "158"}, "connection": "thesylink", "explain": null, "start_percent": 90.485, "width_percent": 1.046}, {"sql": "select count(*) as aggregate from \"projects\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.892364, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:158", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=158", "ajax": false, "filename": "AnalyticsController.php", "line": "158"}, "connection": "thesylink", "explain": null, "start_percent": 91.531, "width_percent": 0.967}, {"sql": "select count(*) as aggregate from \"users\" where \"is_verified\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.895813, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:159", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=159", "ajax": false, "filename": "AnalyticsController.php", "line": "159"}, "connection": "thesylink", "explain": null, "start_percent": 92.498, "width_percent": 0.99}, {"sql": "select count(*) as aggregate from \"users\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.899415, "duration": 0.00722, "duration_str": "7.22ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:159", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=159", "ajax": false, "filename": "AnalyticsController.php", "line": "159"}, "connection": "thesylink", "explain": null, "start_percent": 93.489, "width_percent": 3.341}, {"sql": "select count(*) as aggregate from \"bids\" where \"status\" = 'accepted'", "type": "query", "params": [], "bindings": ["accepted"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.908093, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:160", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=160", "ajax": false, "filename": "AnalyticsController.php", "line": "160"}, "connection": "thesylink", "explain": null, "start_percent": 96.83, "width_percent": 1.05}, {"sql": "select count(*) as aggregate from \"bids\"", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.9113798, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:160", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=160", "ajax": false, "filename": "AnalyticsController.php", "line": "160"}, "connection": "thesylink", "explain": null, "start_percent": 97.881, "width_percent": 1.083}, {"sql": "select avg(\"accepted_bid_amount\") as aggregate from \"projects\" where \"accepted_bid_amount\" is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 165}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.914714, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "AnalyticsController.php:165", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/AnalyticsController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\Admin\\AnalyticsController.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=165", "ajax": false, "filename": "AnalyticsController.php", "line": "165"}, "connection": "thesylink", "explain": null, "start_percent": 98.963, "width_percent": 1.037}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Project": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}}, "count": 18, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 18}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index", "uri": "GET admin/analytics", "controller": "App\\Http\\Controllers\\Admin\\AnalyticsController@index<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FAdmin%2FAnalyticsController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/AnalyticsController.php:21-184</a>", "middleware": "web, auth, verified, admin", "duration": "696ms", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-882655770 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-882655770\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-972999151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-972999151\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2043446597 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">yOgUVcSqFRazcHA8kQ3HOdEiuUIfPJZKRIDkg1k5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im01S0JFVTlxOHhOb1FuOGo2bEdOcmc9PSIsInZhbHVlIjoiajRjRVJlelExV05xQ1ZQRWxENmJObWpMWmZKL29FWm8zdkJ2WGpwempESXpOWjh6Z1RNSkxTMlZPOG1KejJzeC9rUjAydWI1aHRzc3ZqRGtCZGVhejFvMHlwWlE4TGU0UFMzcUM5WHIzZm5sMEZZYUdJZlUvVGpEOEkzM2xMRUYiLCJtYWMiOiIxMTFiODI5OWZhYTY1NzY5ZDU0NTE1NGZiYmYzMTQ0MWY5ZDlmZmFkOGJkM2Q5NTZjNzkwNTFjMWVlZjVlZWU4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">28fd5682706f4a0c8b74154cf5b2bd6d</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"827 characters\">_ga=GA1.1.668794480.1753378234; _ga_69MPZE94D5=GS2.1.s1753378233$o1$g0$t1753378235$j58$l0$h0; appearance=light; XSRF-TOKEN=eyJpdiI6Im01S0JFVTlxOHhOb1FuOGo2bEdOcmc9PSIsInZhbHVlIjoiajRjRVJlelExV05xQ1ZQRWxENmJObWpMWmZKL29FWm8zdkJ2WGpwempESXpOWjh6Z1RNSkxTMlZPOG1KejJzeC9rUjAydWI1aHRzc3ZqRGtCZGVhejFvMHlwWlE4TGU0UFMzcUM5WHIzZm5sMEZZYUdJZlUvVGpEOEkzM2xMRUYiLCJtYWMiOiIxMTFiODI5OWZhYTY1NzY5ZDU0NTE1NGZiYmYzMTQ0MWY5ZDlmZmFkOGJkM2Q5NTZjNzkwNTFjMWVlZjVlZWU4IiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6IkMvSDJFUllha0RIK1FmRjZwaTZiTEE9PSIsInZhbHVlIjoiL2RQUExaT2VnMmtOWTRkQitkNjdZS0ZudkQzL0RRUi9nYzVLU0o1UUQ5SUVvQWVHTXlQNnEwVWtKbWdlQ0lQU09EbzdGRTNnQkhPU3dHdncvZ3ZSeDRmYWprQ1lVSUpQZUgyRlo2cllFLzVPNXpvNjZIUHhmOXB1cnRoZHFXajYiLCJtYWMiOiJiMmQxYzJlMTAwYzRiZjhlMWY4MTM2M2Q4Mzc0YThiYTQwNjdkM2I1MGFiNTk2MzFiOWNiYmRhY2Y5NzQwYjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043446597\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1937999945 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u4zZv8SmaOi0rLwVTeioOekEJ6lX3KQ0r0q4R0r0</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C2p0k1IM1rZwitYlztIXeh1yN0zp0XPtgAcmeUgp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937999945\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-292122585 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 18:38:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292122585\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1391478089 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u4zZv8SmaOi0rLwVTeioOekEJ6lX3KQ0r0q4R0r0</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K4G53SR1H0Y3C8A64FRQHK2S</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">********-e360-7219-aadc-1c26c87b37c1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391478089\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/analytics", "action_name": "admin.analytics.index", "controller_action": "App\\Http\\Controllers\\Admin\\AnalyticsController@index"}, "badge": null}}