{"__meta": {"id": "01K4F4069NM7D4M7936NY0TXT4", "datetime": "2025-09-06 08:59:01", "utime": **********.301736, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[08:59:01] LOG.error: Failed to fetch notifications: Network Error AxiosError AxiosError: Network Error\n    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1615:14)\n    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)\n    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24) true true false xhr http fetch null null 0 XSRF-TOKEN X-XSRF-TOKEN -1 -1  application/json, text/plain, */* XMLHttpRequest 3gH0V8n45B0J0NsopZz5uNCA3oRFF3M2I2UQjag6 get /notifications/dropdown true ERR_NETWORK {\n    \"url\": \"http:\\/\\/localhost:8000\\/wallet\\/add-funds\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-09-06T08:58:58.111Z\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.287856, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.116889, "end": **********.301752, "duration": 0.18486309051513672, "duration_str": "185ms", "measures": [{"label": "Booting", "start": **********.116889, "relative_start": 0, "end": **********.265317, "relative_end": **********.265317, "duration": 0.*****************, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.265339, "relative_start": 0.*****************, "end": **********.301754, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "36.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.282415, "relative_start": 0.****************, "end": **********.284841, "relative_end": **********.284841, "duration": 0.****************, "duration_str": "2.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.299577, "relative_start": 0.*****************, "end": **********.29993, "relative_end": **********.29993, "duration": 0.00035309791564941406, "duration_str": "353μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.299948, "relative_start": 0.****************, "end": **********.299964, "relative_end": **********.299964, "duration": 1.5974044799804688e-05, "duration_str": "16μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1154}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 972}], "start": **********.296645, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=101\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:101-127</a>", "duration": "186ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-849201158 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-09-06T08:58:58.111Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">Failed to fetch notifications:</span>\"\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Network Error</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AxiosError</span>\"\n          \"<span class=sf-dump-key>stack</span>\" => \"\"\"\n            <span class=sf-dump-str title=\"351 characters\">AxiosError: Network Error<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n            <span class=sf-dump-str title=\"351 characters\">    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:1615:14)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n            <span class=sf-dump-str title=\"351 characters\">    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=72859ecd:2143:41)<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\n            <span class=sf-dump-str title=\"351 characters\">    at async fetchNotifications (http://[::1]:5173/resources/js/components/notifications-dropdown.tsx:63:24)</span>\n            \"\"\"\n          \"<span class=sf-dump-key>config</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>transitional</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>silentJSONParsing</span>\" => <span class=sf-dump-const>true</span>\n              \"<span class=sf-dump-key>forcedJSONParsing</span>\" => <span class=sf-dump-const>true</span>\n              \"<span class=sf-dump-key>clarifyTimeoutError</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            \"<span class=sf-dump-key>adapter</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">xhr</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">fetch</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>transformRequest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n            </samp>]\n            \"<span class=sf-dump-key>transformResponse</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n            </samp>]\n            \"<span class=sf-dump-key>timeout</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>xsrfCookieName</span>\" => \"<span class=sf-dump-str title=\"10 characters\">XSRF-TOKEN</span>\"\n            \"<span class=sf-dump-key>xsrfHeaderName</span>\" => \"<span class=sf-dump-str title=\"12 characters\">X-XSRF-TOKEN</span>\"\n            \"<span class=sf-dump-key>maxContentLength</span>\" => <span class=sf-dump-num>-1</span>\n            \"<span class=sf-dump-key>maxBodyLength</span>\" => <span class=sf-dump-num>-1</span>\n            \"<span class=sf-dump-key>env</span>\" => []\n            \"<span class=sf-dump-key>headers</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>Accept</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n              \"<span class=sf-dump-key>X-Requested-With</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n              \"<span class=sf-dump-key>X-CSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3gH0V8n45B0J0NsopZz5uNCA3oRFF3M2I2UQjag6</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n            \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/notifications/dropdown</span>\"\n            \"<span class=sf-dump-key>allowAbsoluteUrls</span>\" => <span class=sf-dump-const>true</span>\n          </samp>]\n          \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ERR_NETWORK</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/wallet/add-funds</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849201158\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1011943173 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1254</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://localhost:8000/wallet/add-funds</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6IkJLbkxDaHptR04yQlU3cjdNd2JrU0E9PSIsInZhbHVlIjoiMkJXMi9kOWFFMGYrZUFqOGJNeVlZclFENzhwditUSmN6QnJrdlJrQzMyTDNnZ1JMc1NTVjJoUEVreEJYazA2RWxaT290VHNoc3JWTndNcWgzc3daRk9uTjBKNTNEb05oSlBsTkdLNHhIbHV2T3Rud3pjbVN2UnVpQmorYWZDSkYiLCJtYWMiOiJhZTZjM2VhNjZlMTJjNjk3NGM4MjczNGEyYzNjNzMwNGYxNDI2N2E0OTc0YTY4YzRiMjM1NTI0YmE3Njc4ZGIyIiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6ImdkcnFmL1pVTXdPUjljTmI5QklsOFE9PSIsInZhbHVlIjoiVCthTEo0U2JRWGR1Y002Y1ROSnYyeGdMYmZ3SHdMcm1HOG1zWVFaRGVJVDdONFBETUEwKzBBQmNmam1XdnVnQ1dpT2JpR24vT1h4bUlmenJHRDVGSHdQenhNcHJYblUwZlNnUFlaVmxxRlNqOVhZN1FPREpneXRRWkpUWk1IL0EiLCJtYWMiOiIwNGVjYTFjMGNiYTNiYjk0NzVjMzcxNDg1ZWY1ODBjMWFiODc4ZjRiOTdhYWQzM2UzYzc1NThhNGY0YTVjYWMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011943173\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1501766453 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkJLbkxDaHptR04yQlU3cjdNd2JrU0E9PSIsInZhbHVlIjoiMkJXMi9kOWFFMGYrZUFqOGJNeVlZclFENzhwditUSmN6QnJrdlJrQzMyTDNnZ1JMc1NTVjJoUEVreEJYazA2RWxaT290VHNoc3JWTndNcWgzc3daRk9uTjBKNTNEb05oSlBsTkdLNHhIbHV2T3Rud3pjbVN2UnVpQmorYWZDSkYiLCJtYWMiOiJhZTZjM2VhNjZlMTJjNjk3NGM4MjczNGEyYzNjNzMwNGYxNDI2N2E0OTc0YTY4YzRiMjM1NTI0YmE3Njc4ZGIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdkcnFmL1pVTXdPUjljTmI5QklsOFE9PSIsInZhbHVlIjoiVCthTEo0U2JRWGR1Y002Y1ROSnYyeGdMYmZ3SHdMcm1HOG1zWVFaRGVJVDdONFBETUEwKzBBQmNmam1XdnVnQ1dpT2JpR24vT1h4bUlmenJHRDVGSHdQenhNcHJYblUwZlNnUFlaVmxxRlNqOVhZN1FPREpneXRRWkpUWk1IL0EiLCJtYWMiOiIwNGVjYTFjMGNiYTNiYjk0NzVjMzcxNDg1ZWY1ODBjMWFiODc4ZjRiOTdhYWQzM2UzYzc1NThhNGY0YTVjYWMzIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501766453\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1682041912 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 06 Sep 2025 08:59:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682041912\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1803940748 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1803940748\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}